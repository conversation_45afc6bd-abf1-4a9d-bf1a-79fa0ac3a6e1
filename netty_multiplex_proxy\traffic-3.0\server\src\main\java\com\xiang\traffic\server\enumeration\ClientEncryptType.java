package com.xiang.traffic.server.enumeration;

/**
 * @<NAME_EMAIL>
 * @since 2021/1/27 2:53
 */
public enum ClientEncryptType {

    NONE("None"), OpenSSL("OpenSSL"), JKS("JKS");

    private final String configValue;

    ClientEncryptType(String configValue) {
        this.configValue = configValue;
    }

    public String configValue() {
        return configValue;
    }

    public static ClientEncryptType configValueOf(String value) {
        for (ClientEncryptType type : values()) {
            if (type.configValue().equals(value)) {
                return type;
            }
        }

        return null;
    }
}
