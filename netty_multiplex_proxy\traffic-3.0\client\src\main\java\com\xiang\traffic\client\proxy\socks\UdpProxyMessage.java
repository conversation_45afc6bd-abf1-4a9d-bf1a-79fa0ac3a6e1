package com.xiang.traffic.client.proxy.socks;

import io.netty.buffer.ByteBuf;
import io.netty.util.ReferenceCounted;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/6/6 15:13
 */
class UdpProxyMessage implements ReferenceCounted {

    private final String host;

    private final int port;

    private final ByteBuf data;

    public UdpProxyMessage(String host, int port, ByteBuf data) {
        if (port <= 0 || port > 65535) {
            throw new IllegalArgumentException("Port number:" + port);
        }

        this.host = Objects.requireNonNull(host);
        this.port = port;
        this.data = Objects.requireNonNull(data);
    }

    public String getHost() {
        return host;
    }

    public int getPort() {
        return port;
    }

    public ByteBuf getData() {
        if (data.refCnt() <= 0) {
            return null;
        }
        return data;
    }

    public boolean isSameTarget(UdpProxyMessage message) {
        return host.equals(message.host) && port == message.port;
    }

    @Override
    public int refCnt() {
        return data.refCnt();
    }

    @Override
    public UdpProxyMessage retain() {
        data.retain();
        return this;
    }

    @Override
    public UdpProxyMessage retain(int increment) {
        data.retain(increment);
        return this;
    }

    @Override
    public UdpProxyMessage touch() {
        data.touch();
        return this;
    }

    @Override
    public UdpProxyMessage touch(Object hint) {
        data.touch(hint);
        return this;
    }

    @Override
    public boolean release(int decrement) {
        return data.release(decrement);
    }

    @Override
    public boolean release() {
        return data.release();
    }

}
