package com.xiang.traffic.client;

import com.xiang.traffic.ConfigEventListener;
import com.xiang.traffic.client.proxy.http.HttpProxyConfig;
import com.xiang.traffic.client.proxy.server.ConnectionStateListener;
import com.xiang.traffic.client.proxy.server.ProxyServerConfig;
import com.xiang.traffic.client.proxy.socks.SocksConfig;

import java.util.Map;

/**
 * GUI操作接口
 *
 * <AUTHOR>
 * @date 2024/6/6 15:15
 */
public interface ClientOperator {

    /**
     * 清空日志
     */
    void cleanLogFiles();

    /**
     * 打开日志目录
     */
    void openLogDirectory();

    /**
     * 打开配置文件目录
     */
    void openConfigDirectory();

    /**
     * 打开浏览器
     *
     * @param url URL链接
     */
    void openBrowser(String url);

    /**
     * 注册配置中心事件监听器
     *
     * @param listener 事件监听器
     */
    void registerConfigEventListener(ConfigEventListener listener);

    /**
     * 注册ProxyServerConfig监听器
     *
     * @param event    关心的事件
     * @param runnable 事件触发后的逻辑
     * @param remove   事件触发后是否删除该监听器
     */
    void registerProxyServerConfigListener(String event, Runnable runnable, boolean remove);

    /**
     * 注册SocksConfig事件监听器
     *
     * @param event    关心的事件
     * @param runnable 事件触发后的逻辑
     * @param remove   事件触发后是否删除该监听器
     */
    void registerSocksConfigListener(String event, Runnable runnable, boolean remove);


    /**
     * 删除配置中心事件监听器
     *
     * @param listener 事件监听器
     */
    void removeConfigEventListener(ConfigEventListener listener);

    /**
     * 用户界面添加代理服务器配置
     *
     * @param node 服务器配置
     */
    void addServerConfig(ProxyServerConfig.Node node);

    /**
     * 更新代理服务器的配置
     *
     * @param node 服务器配置
     */
    void updateServerConfig(ProxyServerConfig.Node node);


    /**
     * 移除代理服务器配置
     *
     * @param node 服务器配置
     */
    void removeServer(ProxyServerConfig.Node node);

    /**
     * @return 服务器配置
     */
    ProxyServerConfig.Node[] getServerNodes();

    /**
     * @return 获取本地Socks代理包装对象
     */
    SocksConfig getSocksConfig();

    /**
     * @return HTTP代理是否开启
     */
    boolean isHttpProxyOpen();

    /**
     * 设置是否打开HTTP代理
     */
    void setupHttpProxySwitch(boolean open);

    /**
     * @return 获取HTTP代理配置对象
     */
    HttpProxyConfig getHttpProxyConfig();


    /**
     * 修改HTTP代理配置
     */
    void updateHttpProxyConfig(boolean open, int port, boolean auth, String username, String password);

    /**
     * 打开Windows系统代理
     */
    void setupWindowsSystemProxy(boolean open);


    /**
     * @return 系统代理模式
     */
    int proxyMode();

    /**
     * @param mode 系统代理模式
     */
    void setProxyMode(int mode);

    /**
     * 启用代理服务器
     *
     * @param node 代理服务器配置
     * @param use  是否启用
     */
    void setProxyServerUsing(ProxyServerConfig.Node node, boolean use);

    /**
     * 批量修改代理服务器状态
     *
     * @param map 代理服务器与是否启用映射关系
     */
    void setProxyServerUsing(Map<ProxyServerConfig.Node, Boolean> map);


    /**
     * 修改本地Socks5代理端口身份验证机制
     *
     * @param port     本地Socks代理端口
     * @param auth     是否打开身份验证
     * @param username 用户名
     * @param password 密码
     */
    void updateSocksProxyAuthentication(int port, boolean auth, String username, String password);

    /**
     * 注册代理服务器连接状态监听器
     *
     * @param listener 监听器对象
     */
    void registerConnectionStateListener(ProxyServerConfig.Node node, ConnectionStateListener listener);

    /**
     * 查询代理服务器上传带宽
     *
     * @param node 代理服务器配置节点
     * @return 上传带宽，单位字节每秒
     */
    long queryProxyServerUploadThroughput(ProxyServerConfig.Node node);


    /**
     * 查询代理服务器下载带宽
     *
     * @param node 代理服务器配置节点
     * @return 下载带宽，单位字节每秒
     */
    long queryProxyServerDownloadThroughput(ProxyServerConfig.Node node);
}
