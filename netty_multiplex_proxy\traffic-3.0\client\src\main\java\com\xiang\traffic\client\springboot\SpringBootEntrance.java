package com.xiang.traffic.client.springboot;//package com.lzf.flyingsocks.client.springboot;
//
//import org.springframework.boot.Banner;
//import org.springframework.boot.SpringApplication;
//import org.springframework.boot.autoconfigure.SpringBootApplication;
//import org.springframework.boot.builder.SpringApplicationBuilder;
//import org.springframework.context.annotation.Bean;
//
///**
// * <AUTHOR>
// * @date 2024/4/22 11:15
// */
//@SpringBootApplication
//public class SpringBootEntrance {
//    @Bean
//    public ApplicationContextHolder applicationContextHolder() {
//        return ApplicationContextHolder.getInstance();
//    }
//
//    public void start(String[] args) {
//        SpringApplication.run(SpringBootEntrance.class);
//        SpringApplicationBuilder builder = new SpringApplicationBuilder();
//        builder.bannerMode(Banner.Mode.OFF);
//        builder.sources(SpringBootEntrance.class).run(args);
//    }
//}
