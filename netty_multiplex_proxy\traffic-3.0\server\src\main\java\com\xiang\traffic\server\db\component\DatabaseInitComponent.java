package com.xiang.traffic.server.db.component;

import com.cn.xiang.mybaits.SpringBootEntrance;
import com.xiang.traffic.AbstractComponent;
import com.xiang.traffic.AbstractConfig;
import com.xiang.traffic.TopLevelComponent;
import com.xiang.traffic.server.StandardServer;
import com.xiang.traffic.server.db.user.DatabaseFactory;
import com.xiang.traffic.server.db.user.UserDatabase;
import com.xiang.traffic.server.enumeration.DbTypeEnum;
import com.xiang.traffic.server.utils.Utils;

/**
 * <AUTHOR>
 * @date 2024/2/5 21:33
 */
public class DatabaseInitComponent extends AbstractComponent<TopLevelComponent> {
    public static final String name = "DatabaseInitComponent";

    private UserDatabase userDatabase;

    public DatabaseInitComponent(TopLevelComponent parent) {
        super(name, parent);
    }

    @Override
    protected void initInternal() {
        super.initInternal();

        DbTypeEnum dbType = Utils.getDbTypeFromSystemProperty();
        if (dbType.equals(DbTypeEnum.MYBATIS_PLUS)) {
            if (log.isInfoEnabled()) {
                log.info("Database type is mybatis_plus, initializing...");
            }
            SpringBootEntrance.run(new String[]{});
        }

        StandardServer server = (StandardServer) this.getParentComponent();
        userDatabase = DatabaseFactory.createUserDatabase(dbType, server.getConfigManager());
        server.getConfigManager().registerConfig((AbstractConfig) userDatabase);
    }

    public UserDatabase getUserDatabase() {
        return this.userDatabase;
    }
}
