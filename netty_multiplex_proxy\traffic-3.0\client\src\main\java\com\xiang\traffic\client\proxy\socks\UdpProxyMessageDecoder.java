package com.xiang.traffic.client.proxy.socks;

import com.xiang.traffic.misc.BaseUtils;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.socket.DatagramPacket;
import io.netty.handler.codec.DecoderException;
import io.netty.handler.codec.MessageToMessageDecoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.Charset;
import java.util.List;

/**
 * 处理Socks5的UDP穿透请求
 * 请求格式如下(RFC1928)：
 * <p>
 * +----+------+------+----------+----------+----------+
 * |RSV | FRAG | ATYP | DST.ADDR | DST.PORT |   DATA   |
 * +----+------+------+----------+----------+----------+
 * | 2  |  1   |   1  | Variable |     2    | Variable |
 * +----+------+------+----------+----------+----------+
 * o RSV Reserved X’0000’
 * o FRAG Current fragment number
 * o ATYP address type of following addresses:
 * IP V4 address: X’01’
 * DOMAINNAME: X’03’
 * IP V6 address: X’04’
 * o DST.ADDR desired destination address
 * o DST.PORT desired destination port
 * o DATA user data
 *
 * <AUTHOR>
 * @date 2024/6/6 15:13
 */

class UdpProxyMessageDecoder extends MessageToMessageDecoder<DatagramPacket> {

    private static final Logger log = LoggerFactory.getLogger(UdpProxyMessageDecoder.class);

    private static final Charset UNICODE = Charset.forName("Unicode");

    private final ReassemblyQueue reassemblyQueue = new BaseReassemblyQueue();

    @Override
    protected final void decode(ChannelHandlerContext ctx, DatagramPacket packet, List<Object> out) {
        ByteBuf buf = packet.content();
        short rsv = buf.readShort();
        if (rsv != 0) {
            throw new DecoderException("Illegal field RSV");
        }

        byte frag = buf.readByte();
        byte atyp = buf.readByte();

        String dstAddr = obtainTargetHost(buf, atyp);
        int dstPort = buf.readUnsignedShort();
        ByteBuf data = buf.readRetainedSlice(buf.readableBytes());

        if (frag == 0) {
            out.add(new UdpProxyMessage(dstAddr, dstPort, data));
            return;
        }

        UdpProxyMessage msg = reassemblyQueue.tryAppendAndGet(frag, new UdpProxyMessage(dstAddr, dstPort, data));
        if (msg != null) {
            out.add(msg);
        }
    }


    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        if (cause instanceof DecoderException) {
            log.warn("An Exception occur at UdpProxyMessageDecoder [{}]", cause.getMessage());
            ctx.close();
            return;
        }

        ctx.fireExceptionCaught(cause);
    }

    private String obtainTargetHost(ByteBuf buf, byte atyp) {
        switch (atyp) {
            case 0x01:
                return BaseUtils.parseIntToIPv4Address(buf.readInt());
            case 0x03:
                short len = buf.readUnsignedByte();
                byte[] host = new byte[len];
                buf.readBytes(host);
                return new String(host, UNICODE);
            case 0x04:
                byte[] ipv6 = new byte[16];
                buf.readBytes(ipv6);
                return BaseUtils.parseByteArrayToIPv6Address(ipv6);
        }

        throw new DecoderException("Illegal field ATYP");
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        reassemblyQueue.reset();
        super.channelInactive(ctx);
    }
}

