<?xml version="1.0" encoding="utf-8" ?>
<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.2.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.2.0 https://maven.apache.org/xsd/assembly-2.2.0.xsd">
    <id>bin</id>
    <formats>
        <format>zip</format>
    </formats>
    <dependencySets>

        <!--依赖包打包到lib下,false lib下不包含本工程包, true:包含-->
        <dependencySet>
            <useProjectArtifact>true</useProjectArtifact>
            <outputDirectory>lib</outputDirectory>
        </dependencySet>
    </dependencySets>

    <fileSets>
        <fileSet>
            <outputDirectory>/</outputDirectory>
            <includes>
                <include>README.md</include>
            </includes>
        </fileSet>

        <fileSet>
            <outputDirectory>/</outputDirectory>
            <includes>
                <include>LICENSE</include>
            </includes>
        </fileSet>

        <!-- 打包启停脚本到bin下 -->
        <fileSet>
            <directory>src/main/shell</directory>
            <outputDirectory>/bin</outputDirectory>
            <includes>
                <include>*</include>
            </includes>
        </fileSet>

        <!-- 打包启停脚本到bin下 -->
        <fileSet>
            <directory>src/main/batch</directory>
            <outputDirectory>/bin</outputDirectory>
            <includes>
                <include>*</include>
            </includes>
        </fileSet>

        <!-- 打包配置文件到config下 -->
        <!--    <fileSet>
                <directory>src/main/resources</directory>
                <outputDirectory>/config</outputDirectory>
                <includes>
                    <include>*</include>
                    <include>*/**</include>
                </includes>
            </fileSet>-->

        <fileSet>
            <directory>src/main/resources</directory>
            <outputDirectory>/config</outputDirectory>
            <includes>
                <include>**/*.*</include>
                <include>**/*</include>
            </includes>
            <excludes>
                <exclude>application*</exclude>
            </excludes>
        </fileSet>
        <fileSet>
            <directory>src/main/resources</directory>
            <outputDirectory>/config</outputDirectory>
            <includes>
                <include>application-${package.environment}.yml</include>
                <include>application.yml</include>
            </includes>
            <filtered>true</filtered>
        </fileSet>
    </fileSets>
</assembly>
