package com.xiang.traffic.client.proxy.direct;

import com.xiang.traffic.AbstractComponent;
import com.xiang.traffic.client.proxy.ProxyComponent;
import com.xiang.traffic.client.proxy.ProxyRequest;
import com.xiang.traffic.client.proxy.ProxyRequestSubscriber;
import com.xiang.traffic.client.proxy.misc.MessageDelivererCancelledException;
import com.xiang.traffic.client.proxy.misc.MessageReceiver;
import com.xiang.traffic.misc.BootstrapTemplate;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.socket.DatagramPacket;
import io.netty.channel.socket.nio.NioDatagramChannel;

import java.net.InetSocketAddress;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/6/6 15:03
 */
public class DatagramForwardComponent extends AbstractComponent<ProxyComponent> implements ProxyRequestSubscriber {

    private final BootstrapTemplate bootstrapTemplate;

    private final EventLoopGroup eventLoopGroup;

    public DatagramForwardComponent(ProxyComponent component) {
        super("DatagramForwardComponent", Objects.requireNonNull(component));
        Bootstrap bootstrap = new Bootstrap();
        bootstrap.group(this.eventLoopGroup = parent.createNioEventLoopGroup(2))
                .channel(NioDatagramChannel.class);

        this.bootstrapTemplate = new BootstrapTemplate(bootstrap);
    }


    @Override
    protected void startInternal() {
        parent.registerSubscriber(this);
        super.startInternal();
    }

    @Override
    protected void stopInternal() {
        eventLoopGroup.shutdownGracefully();
        super.stopInternal();
    }

    @Override
    public void receive(ProxyRequest request) {
        bootstrapTemplate.doBind(0, new ForwardHandler(request), future -> {
            if (!future.isSuccess()) {
                request.close();
            }
            request.addClientChannelCloseListener(f -> future.channel().close());
        });
    }

    @Override
    public boolean receiveNeedlessProxy() {
        return true;
    }

    @Override
    public boolean receiveNeedProxy() {
        return false;
    }

    @Override
    public Set<ProxyRequest.Protocol> requestProtocol() {
        return ProxyRequestSubscriber.ONLY_UDP;
    }


    private class ForwardHandler extends ChannelInboundHandlerAdapter {

        private final ProxyRequest proxyRequest;

        ForwardHandler(ProxyRequest proxyRequest) {
            this.proxyRequest = Objects.requireNonNull(proxyRequest);
        }

        @Override
        public void channelActive(ChannelHandlerContext ctx) throws Exception {
            InetSocketAddress address = new InetSocketAddress(proxyRequest.getHost(), proxyRequest.getPort());

            proxyRequest.setClientMessageReceiver(new MessageReceiver() {
                @Override
                public void receive(ByteBuf message) {
                    DatagramPacket packet = new DatagramPacket(message, address);
                    ctx.writeAndFlush(packet, ctx.voidPromise());
                }

                @Override
                public void close() {
                    ctx.close();
                }
            });

            ctx.fireChannelActive();
        }

        @Override
        public void channelRead(ChannelHandlerContext ctx, Object msg) {
            if (msg instanceof DatagramPacket) {
                DatagramPacket packet = (DatagramPacket) msg;
                proxyRequest.clientChannel().writeAndFlush(packet);
            } else {
                ctx.fireChannelRead(msg);
            }
        }

        @Override
        public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
            if (cause instanceof MessageDelivererCancelledException) {
                ctx.close();
                return;
            }

            log.error("An error occur in ForwardHandler", cause);
        }
    }
}
