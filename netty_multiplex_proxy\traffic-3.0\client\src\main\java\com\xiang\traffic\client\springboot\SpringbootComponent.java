package com.xiang.traffic.client.springboot;//package com.lzf.flyingsocks.client.springboot;
//
//import com.lzf.flyingsocks.AbstractComponent;
//import com.lzf.flyingsocks.client.Client;
//import org.springframework.cloud.client.ServiceInstance;
//import org.springframework.cloud.client.discovery.DiscoveryClient;
//
//import java.util.Objects;
//
///**
// * 启动springboot环境
// *
// * <AUTHOR>
// * @date 2024/4/22 11:23
// */
//public class SpringbootComponent extends AbstractComponent<Client> {
//    private static final String NAME = "springbootComponent";
//
//    public SpringbootComponent(Client client) {
//        super(NAME, Objects.requireNonNull(client));
//    }
//
//    @Override
//    protected void startInternal() {
//        super.startInternal();
//
//        SpringBootEntrance springBootEntrance = new SpringBootEntrance();
//        springBootEntrance.start(new String[]{});
//
//        DiscoveryClient discoveryClient= ApplicationContextHolder.getBean(DiscoveryClient.class);
//        for(ServiceInstance instance: discoveryClient.getInstances("FSOCKS")){
//            System.out.println(instance.getHost());
//        }
//    }
//}
