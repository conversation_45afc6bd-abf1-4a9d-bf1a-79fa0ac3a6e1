package com.xiang.traffic.client.proxy.server;

import com.xiang.traffic.AbstractConfig;
import com.xiang.traffic.ConfigInitializationException;
import com.xiang.traffic.ConfigManager;
import com.xiang.traffic.client.GlobalConfig;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;


public class OpenSSLConfig extends AbstractConfig {
    private static final String NAME_PREFIX = "config.OpenSSL.";

    public static final String CERT_FILE_NAME = "ca.crt";

    private final String host;

    private final int port;

    private Path rootCertFile;

    OpenSSLConfig(ConfigManager<?> configManager, String host, int port) {
        super(configManager, generalName(host, port));
        this.host = host;
        this.port = port;
    }

    public static String generalName(String host, int port) {
        return NAME_PREFIX + host + "#" + port;
    }

    public static String folderName(String host, int port) {
        return host + "#" + port;
    }

    @Override
    protected void initInternal() throws ConfigInitializationException {
        try {
            loadResource();
        } catch (Exception e) {
            throw new ConfigInitializationException(e);
        }
    }

    private synchronized void loadResource() throws Exception {
        GlobalConfig cfg = configManager.getConfig(GlobalConfig.NAME, GlobalConfig.class);
        this.rootCertFile = cfg.configPath().resolve(folderName(host, port)).resolve(CERT_FILE_NAME);
        if (!Files.exists(rootCertFile)) {
            throw new FileNotFoundException("CA cert file at location " + rootCertFile + " doesn't exists.");
        }
    }


    public InputStream openRootCertStream() throws IOException {
        return Files.newInputStream(rootCertFile);
    }
}
