package com.xiang.traffic.client.proxy.transparent;

import io.netty.channel.epoll.EpollSocketChannel;
import io.netty.util.internal.NativeLibraryLoader;
import io.netty.util.internal.PlatformDependent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetSocketAddress;
import java.util.Objects;

/**
 * 透明代理本地接口
 *
 * <AUTHOR>
 * @date 2024/6/6 15:14
 */
final class LinuxNative {

    private static final Logger log = LoggerFactory.getLogger(LinuxNative.class);

    static {
        String libName = "libflyingsocks_transparent_" + PlatformDependent.normalizedArch();
        log.info("Load transparent native lib: {}", libName);

        ClassLoader loader = PlatformDependent.getClassLoader(LinuxNative.class);
        try {
            NativeLibraryLoader.load(libName, loader);
        } catch (UnsatisfiedLinkError e) {
            log.error("Load transparent native lib failure", e);
            throw e;
        }
    }

    /**
     * @return 目标地址
     */
    public static InetSocketAddress getTargetAddress(EpollSocketChannel channel) {
        Objects.requireNonNull(channel);
        return getTargetAddress0(channel.fd().intValue());
    }


    private native static InetSocketAddress getTargetAddress0(int fd);


    private LinuxNative() {
    }
}
