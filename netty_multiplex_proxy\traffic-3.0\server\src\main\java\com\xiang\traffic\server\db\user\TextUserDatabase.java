package com.xiang.traffic.server.db.user;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xiang.traffic.*;
import com.xiang.traffic.server.ServerConfig;
import com.xiang.traffic.server.db.vo.DbUserVo;
import com.xiang.traffic.server.utils.Constants;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.util.Collections;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 轻量级用户数据库实现：基于文本文件实现
 */
public class TextUserDatabase extends AbstractConfig implements UserDatabase {
    private static final Logger log = LoggerFactory.getLogger("TextUserDatabase");

    public static final String NAME = "user.json";

    private final Map<String, UserGroupImpl> groupMap = new ConcurrentHashMap<>(8);

    /**
     * 用户组对象
     */
    public static final class UserGroupImpl implements UserGroup {
        private final String name;
        private int defaultWriteLimit;
        private int defaultReadLimit;
        private final ConcurrentMap<String, DbUserVo> userMap = new ConcurrentHashMap<>(8);

        @Override
        public String name() {
            return name;
        }

        public UserGroupImpl(String name) {
            this.name = name;
        }

        @Override
        public int getDefaultWriteLimit() {
            return defaultWriteLimit;
        }

        public void setDefaultWriteLimit(int defaultWriteLimit) {
            this.defaultWriteLimit = defaultWriteLimit;
        }

        @Override
        public int getDefaultReadLimit() {
            return defaultReadLimit;
        }

        public void setDefaultReadLimit(int defaultReadLimit) {
            this.defaultReadLimit = defaultReadLimit;
        }

        public Map<String, DbUserVo> getUserMap() {
            return Collections.unmodifiableMap(userMap);
        }

        public void setUserMap(Map<String, DbUserVo> userMap) {
            this.userMap.putAll(userMap);
        }
    }

    public TextUserDatabase(ConfigManager<?> configManager) {
        super(configManager, NAME);
    }

    @Override
    protected void initInternal() throws ConfigInitializationException {
        ServerConfig cfg = configManager.getConfig(ServerConfig.NAME, ServerConfig.class);
        if (cfg != null) {
            doInitial(cfg);
        } else {
            configManager.registerConfigEventListener(new ConfigEventListener() {
                @Override
                public void configEvent(ConfigEvent event) {
                    if (event.getEvent().equals(Config.REGISTER_EVENT) && event.getSource() instanceof ServerConfig) {
                        try {
                            doInitial((ServerConfig) event.getSource());
                        } catch (ConfigInitializationException e) {
                            log.error("Load UserDatabase occur a exception", e);
                        }
                        configManager.removeConfigEventListener(this);
                    }
                }
            });
        }
    }


    private void doInitial(ServerConfig cfg) throws ConfigInitializationException {
        Path path = cfg.getLocation().resolve(this.getName());
        try (InputStream is = new FileInputStream(path.toFile())) {
            byte[] b = new byte[512000];
            int len = is.read(b);
            String json = new String(b, 0, len, StandardCharsets.UTF_8);

            JSONArray arr = JSON.parseArray(json);
            for (int i = 0; i < arr.size(); i++) {
                JSONObject obj = arr.getJSONObject(i);
                UserGroupImpl group = new UserGroupImpl(obj.getString(Constants.GROUP_PARAM_NAME));

                String defaultReadLimit = obj.getString(Constants.DEFAULT_READ_LIMIT_PARAM_NAME);
                group.setDefaultReadLimit(StringUtils.isEmpty(defaultReadLimit) ? 0 : Integer.parseInt(defaultReadLimit));

                String defaultWriteLimit = obj.getString(Constants.DEFAULT_WRITE_LIMIT_PARAM_NAME);
                group.setDefaultWriteLimit(StringUtils.isEmpty(defaultWriteLimit) ? 0 : Integer.parseInt(defaultWriteLimit));

                JSONArray userArr = obj.getJSONArray(Constants.USER_PARAM_NAME);
                for (int j = 0; j < userArr.size(); j++) {
                    JSONObject user = userArr.getJSONObject(j);

                    DbUserVo userVo = new DbUserVo();
                    userVo.setUsername(user.getString(Constants.USER_PARAM_NAME));
                    userVo.setPass(user.getString(Constants.PASS_PARAM_NAME));

                    String readLimit = user.getString(Constants.READ_LIMIT_PARAM_NAME);
                    userVo.setReadLimit(StringUtils.isEmpty(readLimit) ? 0 : Integer.parseInt(readLimit));

                    String writeLimit = user.getString(Constants.WRITE_LIMIT_PARAM_NAME);
                    userVo.setWriteLimit(StringUtils.isEmpty(writeLimit) ? 0 : Integer.parseInt(writeLimit));

                    userVo.setGroup(group.name());

                    group.userMap.put(userVo.getUsername(), userVo);
                }

                if (groupMap.containsKey(group.name))
                    log.warn("Group name '{}' already exists", group.name);
                groupMap.put(group.name, group);
            }

        } catch (IOException ignore) {
            log.info("Can not open user db file user.json");
        } catch (Exception e) {
            throw new ConfigInitializationException(e);
        }
    }


    @Override
    public boolean doAuth(String group, String username, String password) {
        UserGroupImpl ug = groupMap.get(group);
        if (ug == null)
            return false;
        DbUserVo user = ug.userMap.get(username);
        return (password == null ? "" : password).equals(user.getPass());
    }

    @Override
    public boolean register(String group, DbUserVo userVo) {
        UserGroupImpl ug = groupMap.get(group);
        if (ug == null)
            return false;

        if (ug.userMap.containsKey(userVo.getUsername()))
            return false;

        ug.userMap.put(userVo.getUsername(), userVo);
        return true;
    }

    @Override
    public boolean delete(String group, String username) {
        UserGroupImpl ug = groupMap.get(group);
        if (ug == null)
            return false;

        return ug.userMap.remove(username) != null;
    }

    @Override
    public boolean changePassword(String group, String username, String newPassword) {
        UserGroupImpl ug = groupMap.get(group);
        if (ug == null)
            return false;

        if (!ug.userMap.containsKey(username))
            return false;
        DbUserVo userVo = ug.userMap.get(username);
        userVo.setPass(newPassword);
        return true;
    }

    public UserGroup getUserGroup(String groupName) {
        return groupMap.get(groupName);
    }

    @Override
    public long getUserTrafficCapacity(String username) {
        return 0;
    }

    @Override
    public long getUserTrafficUsage(String username) {
        return 0;
    }

    @Override
    public long getUserTrafficRemainingCapacity(String username) {
        return 0;
    }
}
