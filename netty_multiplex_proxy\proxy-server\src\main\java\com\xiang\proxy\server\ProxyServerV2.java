package com.xiang.proxy.server;

import com.xiang.proxy.server.config.ProxyServerV2ConfigManager;
import com.xiang.proxy.server.core.ProxyProcessor;
import com.xiang.proxy.server.core.ProxyProcessorFactory;
import com.xiang.proxy.server.inbound.InboundServerManager;
import com.xiang.proxy.server.inbound.impl.multiplex.MultiplexInboundServer;
import com.xiang.proxy.server.inbound.InboundServerConfig;
import com.xiang.proxy.server.outbound.OutboundHandlerType;
import com.xiang.proxy.server.outbound.impl.AsyncTcpDirectOutboundHandler;
import com.xiang.proxy.server.outbound.impl.UdpDirectOutboundHandler;
import com.xiang.proxy.server.outbound.OutboundConfig;
import com.xiang.proxy.server.router.DefaultRouter;
import com.xiang.proxy.server.router.Router;
import com.xiang.proxy.server.router.RouteRule;
import com.xiang.proxy.server.router.RouteMatcher;
import com.xiang.proxy.server.config.properties.ProxyServerV2Properties;
import com.xiang.proxy.server.util.ThreadPoolPerformanceAnalyzer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 代理服务器V2 - 基于组件化架构
 * 整合Inbound、Router、Outbound三个核心组件
 */
public class ProxyServerV2 {
    private static final Logger logger = LoggerFactory.getLogger(ProxyServerV2.class);

    // 核心组件
    private final Router router;
    private final ProxyProcessor proxyProcessor;
    private final InboundServerManager inboundServerManager;

    // 配置
    private final ProxyServerV2Properties config;
    private ScheduledExecutorService monitoringExecutor;

    // 服务器状态
    private volatile boolean running = false;

    public ProxyServerV2() {
        logger.info("初始化代理服务器V2 - 组件化架构");

        // 加载配置
        this.config = ProxyServerV2ConfigManager.getInstance().getProperties();

        // 创建核心组件
        this.router = new DefaultRouter();
//        this.proxyProcessor = ProxyProcessorFactory.createAutoProcessor( router);
        this.proxyProcessor = ProxyProcessorFactory.createStandardProcessor( router);
        this.inboundServerManager = new InboundServerManager(proxyProcessor);

        logger.info("代理服务器V2核心组件初始化完成");
    }

    public ProxyServerV2(ProxyServerV2Properties config) {
        logger.info("使用指定配置初始化代理服务器V2");

        this.config = config;
        this.router = new DefaultRouter();
        this.proxyProcessor = new ProxyProcessor(router);
        this.inboundServerManager = new InboundServerManager(proxyProcessor);

        logger.info("代理服务器V2核心组件初始化完成");
    }

    /**
     * 启动代理服务器
     */
    public CompletableFuture<Void> start() {
        if (running) {
            return CompletableFuture.completedFuture(null);
        }

        logger.info("启动代理服务器V2...");

        return CompletableFuture.runAsync(() -> {
            try {
                // 1. 初始化系统组件
                initializeSystemComponents();

                // 2. 配置路由规则
                setupRoutes();

                // 3. 注册Outbound处理器
                setupOutbounds();

                // 4. 启动连接池清理器（包含连接池启动）
                startConnectionCleaner();

                // 5. 启动代理处理器
                startProxyProcessor();

                // 6. 创建和配置Inbound服务器
                setupInboundServers();

                // 7. 启动所有Inbound服务器
                startInboundServers();

                // 8. 启动监控
                startMonitoring();

                // 9. 注册关闭钩子
                registerShutdownHook();

                running = true;
                logger.info("代理服务器V2启动完成");

            } catch (Exception e) {
                logger.error("启动代理服务器V2失败", e);
                throw new RuntimeException("Failed to start ProxyServerV2", e);
            }
        });
    }

    /**
     * 启动代理处理器
     */
    private void startProxyProcessor() {
        this.proxyProcessor.start();
    }

    /**
     * 停止代理服务器
     */
    public CompletableFuture<Void> stop() {
        if (!running) {
            return CompletableFuture.completedFuture(null);
        }

        logger.info("停止代理服务器V2...");

        return CompletableFuture.runAsync(() -> {
            try {
                running = false;

                // 1. 停止监控服务
                stopMonitoring();

                // 2. 停止所有Inbound服务器
                stopInboundServers();

                // 3. 关闭代理处理器
                proxyProcessor.shutdown();

                // 4. 清理路由规则
                router.clearRoutes();

                logger.info("代理服务器V2停止完成");

            } catch (Exception e) {
                logger.error("停止代理服务器V2时发生异常", e);
            }
        });
    }

    /**
     * 初始化系统组件
     */
    private void initializeSystemComponents() {
        logger.info("初始化系统组件...");

        try {
            // 这里可以初始化其他系统组件
            // 暂时移除对不存在类的引用
            logger.info("系统组件初始化完成");

        } catch (Exception e) {
            logger.error("初始化系统组件失败", e);
            logger.warn("将使用默认配置继续启动服务器");
        }
    }

    /**
     * 配置路由规则
     */
    private void setupRoutes() {
        logger.info("配置路由规则...");

        // 从配置文件加载路由规则
        for (ProxyServerV2Properties.RouteRuleProperties ruleConfig : config.getRouting().getRules()) {
            if (!ruleConfig.isEnabled()) {
                continue;
            }

            RouteRule rule = new RouteRule(ruleConfig.getId(), ruleConfig.getName(),
                    ruleConfig.getPriority(), ruleConfig.getOutbound());

            // 添加匹配器
            for (ProxyServerV2Properties.RouteMatcherConfig matcherConfig : ruleConfig.getMatchers()) {
                RouteMatcher matcher = new RouteMatcher(matcherConfig.getType(),
                        matcherConfig.getOperator(), matcherConfig.getValue());
                matcher.setCaseSensitive(matcherConfig.isCaseSensitive());
                rule.addMatcher(matcher);
            }

            router.addRoute(rule);
        }

        // 如果没有配置路由规则，添加默认规则
        if (config.getRouting().getRules().isEmpty()) {
            // 内网直连路由
            RouteRule internalRule = new RouteRule("internal-direct", "内网直连", 10,
                    config.getRouting().getDefaultOutbound());
            internalRule.addMatcher(new RouteMatcher(RouteMatcher.Type.HOST, RouteMatcher.Operator.REGEX,
                    "^(192\\.168\\.|10\\.|172\\.(1[6-9]|2[0-9]|3[01])\\.).*"));
            router.addRoute(internalRule);

            // 本地域名直连路由
            RouteRule localRule = new RouteRule("local-direct", "本地域名直连", 20,
                    config.getRouting().getDefaultOutbound());
            localRule.addMatcher(new RouteMatcher(RouteMatcher.Type.HOST, RouteMatcher.Operator.ENDS_WITH, ".local"));
            router.addRoute(localRule);

            // 默认路由规则（优先级最低）
            RouteRule defaultRule = new RouteRule("default-direct", "默认路由", 999,
                    config.getRouting().getDefaultOutbound());
            router.addRoute(defaultRule);
        }

        logger.info("路由规则配置完成，共配置{}条规则", router.getRoutes().size());
    }

    /**
     * 注册Outbound处理器
     */
    private void setupOutbounds() {
        logger.info("注册Outbound处理器...");

        // 从配置文件加载Outbound处理器
        for (ProxyServerV2Properties.OutboundProperties outboundConfig : config.getOutbounds()) {
            if (!outboundConfig.isEnabled()) {
                continue;
            }

            try {
                createAndRegisterOutbound(outboundConfig);
            } catch (Exception e) {
                logger.error("创建Outbound处理器失败: {}", outboundConfig.getId(), e);
            }
        }

        logger.info("Outbound处理器注册完成");
    }

    /**
     * 创建并注册Outbound处理器
     */
    private void createAndRegisterOutbound(ProxyServerV2Properties.OutboundProperties outboundConfig) {
        String id = outboundConfig.getId();

        OutboundHandlerType type = OutboundHandlerType.valueOf(outboundConfig.getType().toUpperCase());
        switch (type) {
            case TCP_DIRECT:
                createTcpDirectOutbound(outboundConfig);
                break;
            case UDP_DIRECT:
                createUdpDirectOutbound(outboundConfig);
                break;
//            case "proxy_chain":
//                logger.warn("代理链Outbound暂未实现: {}", id);
//                break;
//            case "load_balancer":
//                logger.warn("负载均衡Outbound暂未实现: {}", id);
//                break;
            default:
                logger.warn("未知的Outbound类型: {} ({})", type, id);
        }
    }

    /**
     * 创建TCP直连Outbound
     */
    private void createTcpDirectOutbound(ProxyServerV2Properties.OutboundProperties outboundProperties) {
        OutboundConfig config = new OutboundConfig();

        // 从配置中读取参数，使用安全的类型转换
        config.setConnectTimeout(getIntValue(outboundProperties, "connect_timeout", 5000));
        config.setReadTimeout(getIntValue(outboundProperties, "read_timeout", 30000));
        config.setWriteTimeout(getIntValue(outboundProperties, "write_timeout", 30000));
        config.setMaxRetries(getIntValue(outboundProperties, "max_retries", 3));
        config.setRetryDelay(getLongValue(outboundProperties, "retry_delay", 1000L));

        config.setKeepAlive(getBooleanValue(outboundProperties, "keep_alive", true));
        config.setTcpNoDelay(getBooleanValue(outboundProperties, "tcp_no_delay", true));
        config.setReceiveBufferSize(getIntValue(outboundProperties, "receive_buffer_size", 65536));
        config.setSendBufferSize(getIntValue(outboundProperties, "send_buffer_size", 65536));

        // 使用异步TCP直连处理器，支持连接建立过程中的数据缓存
        AsyncTcpDirectOutboundHandler outbound = new AsyncTcpDirectOutboundHandler(outboundProperties.getId(), config);
        proxyProcessor.registerOutboundHandler(outbound);

        logger.info("注册TCP直连Outbound: {} ({})", outboundProperties.getName(), outboundProperties.getId());
    }

    /**
     * 创建UDP直连Outbound
     */
    private void createUdpDirectOutbound(ProxyServerV2Properties.OutboundProperties outboundProperties) {
        OutboundConfig config = new OutboundConfig();

        // 从配置中读取参数，使用安全的类型转换
        config.setConnectTimeout(getIntValue(outboundProperties, "connect_timeout", 5000));
        config.setReadTimeout(getIntValue(outboundProperties, "read_timeout", 30000));
        config.setWriteTimeout(getIntValue(outboundProperties, "write_timeout", 30000));
        config.setMaxRetries(getIntValue(outboundProperties, "max_retries", 3));
        config.setRetryDelay(getLongValue(outboundProperties, "retry_delay", 1000L));

        // UDP特定配置
        config.setReceiveBufferSize(getIntValue(outboundProperties, "receive_buffer_size", 65536));
        config.setSendBufferSize(getIntValue(outboundProperties, "send_buffer_size", 65536));

        UdpDirectOutboundHandler outbound = new UdpDirectOutboundHandler(outboundProperties.getId(), config);
        proxyProcessor.registerOutboundHandler(outbound);

        logger.info("注册UDP直连Outbound: {} ({})", outboundProperties.getName(), outboundProperties.getId());
    }

    /**
     * 创建和配置Inbound服务器
     */
    private void setupInboundServers() {
        logger.info("配置Inbound服务器...");

        // 从配置文件加载Inbound服务器
        for (ProxyServerV2Properties.InboundProperties inboundProperties : config.getInbounds()) {
            if (!inboundProperties.isEnabled()) {
                continue;
            }

            try {
                createAndRegisterInbound(inboundProperties);
            } catch (Exception e) {
                logger.error("创建Inbound服务器失败: {}", inboundProperties.getId(), e);
            }
        }

        logger.info("Inbound服务器配置完成");
    }

    /**
     * 创建并注册Inbound服务器
     */
    private void createAndRegisterInbound(ProxyServerV2Properties.InboundProperties inboundProperties) {
        String type = inboundProperties.getType();
        String id = inboundProperties.getId();

        switch (type) {
            case "multiplex":
                createMultiplexInbound(inboundProperties);
                break;
            case "http":
                logger.warn("HTTP Inbound暂未实现: {}", id);
                break;
            case "socks5":
                logger.warn("SOCKS5 Inbound暂未实现: {}", id);
                break;
            default:
                logger.warn("未知的Inbound类型: {} ({})", type, id);
        }
    }

    /**
     * 创建多路复用Inbound
     */
    private void createMultiplexInbound(ProxyServerV2Properties.InboundProperties inboundProperties) {
        InboundServerConfig config = new InboundServerConfig();

        // 从配置中读取参数，使用安全的类型转换
        config.setPort(getIntValue(inboundProperties, "port", 8080));
        config.setBindAddress(getStringValue(inboundProperties, "bind_address", "0.0.0.0"));
        config.setBossThreads(getIntValue(inboundProperties, "boss_threads", 2));
        config.setWorkerThreads(getIntValue(inboundProperties, "worker_threads", 0));
        config.setBacklog(getIntValue(inboundProperties, "backlog", 2048));
        config.setMaxConnections(getIntValue(inboundProperties, "max_connections", 50000));
        config.setReceiveBufferSize(getIntValue(inboundProperties, "receive_buffer_size", 131072));
        config.setSendBufferSize(getIntValue(inboundProperties, "send_buffer_size", 131072));
        config.setKeepAlive(getBooleanValue(inboundProperties, "keep_alive", true));
        config.setTcpNoDelay(getBooleanValue(inboundProperties, "tcp_no_delay", true));
        config.setReuseAddress(getBooleanValue(inboundProperties, "reuse_address", true));
        config.setSoTimeout(getIntValue(inboundProperties, "so_timeout", 30000));
        config.setConnectTimeout(getIntValue(inboundProperties, "connect_timeout", 5000));

        // SSL配置
        config.setEnableSsl(getBooleanValue(inboundProperties, "enable_ssl", false));

        MultiplexInboundServer server = new MultiplexInboundServer(inboundProperties.getId(), config, proxyProcessor);
        inboundServerManager.registerServer(server);

        logger.info("注册多路复用Inbound: {} ({}) - 端口: {}",
                inboundProperties.getName(), inboundProperties.getId(), config.getPort());
    }

    /**
     * 启动所有Inbound服务器
     */
    private void startInboundServers() {
        logger.info("启动所有Inbound服务器...");

        try {
            inboundServerManager.startAll().get(30, TimeUnit.SECONDS);

            InboundServerManager.ManagerStatistics stats = inboundServerManager.getStatistics();
            logger.info("Inbound服务器启动完成: {}", stats);

        } catch (Exception e) {
            logger.error("启动Inbound服务器失败", e);
            throw new RuntimeException("Failed to start inbound servers", e);
        }
    }

    /**
     * 停止所有Inbound服务器
     */
    private void stopInboundServers() {
        logger.info("停止所有Inbound服务器...");

        try {
            inboundServerManager.stopAll().get(10, TimeUnit.SECONDS);
            logger.info("所有Inbound服务器已停止");

        } catch (Exception e) {
            logger.warn("停止Inbound服务器时发生异常", e);
        }
    }

    /**
     * 启动连接清理器 (已移除ConnectionPool，保留方法以维持兼容性)
     */
    private void startConnectionCleaner() {
        // ConnectionPool已移除，使用AsyncTcpDirectOutboundHandler的内置连接管理
        logger.info("使用异步连接管理，无需启动传统连接池");
    }

    /**
     * 启动监控服务
     */
    private void startMonitoring() {
        if (!config.getMetrics().isEnable()) {
            logger.info("性能监控已禁用");
            return;
        }

        logger.info("启动监控服务...");

        monitoringExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "ProxyServerV2-Monitor");
            t.setDaemon(true);
            return t;
        });

        int intervalSeconds = config.getMetrics().getReport().getInterval().getTotalSeconds();
        monitoringExecutor.scheduleWithFixedDelay(() -> {
            try {
                logSystemMetrics();
                logServerStatistics();
                performHealthCheck();

                // 输出连接管理统计信息
                try {
                    // 输出活跃连接统计
                    logger.info("活跃连接数: {}", proxyProcessor.getActiveConnectionCount());

                    // 输出Outbound处理器统计
                    for (ProxyServerV2Properties.OutboundProperties outboundConfig : config.getOutbounds()) {
                        if (outboundConfig.isEnabled() && "tcp_direct".equals(outboundConfig.getType())) {
                            try {
                                var handler = proxyProcessor.getOutboundHandler(outboundConfig.getId());
                                if (handler instanceof AsyncTcpDirectOutboundHandler) {
                                    AsyncTcpDirectOutboundHandler asyncHandler = (AsyncTcpDirectOutboundHandler) handler;
                                    logger.info("异步TCP处理器统计: {}", asyncHandler.getStats());
                                }
                            } catch (Exception ex) {
                                logger.debug("获取Outbound统计失败: {}", outboundConfig.getId(), ex);
                            }
                        }
                    }
                } catch (Exception e) {
                    logger.debug("获取连接管理统计失败", e);
                }

            } catch (Exception e) {
                logger.warn("监控任务执行异常", e);
            }
        }, intervalSeconds, intervalSeconds, TimeUnit.SECONDS);

        logger.info("监控服务启动完成，每{}秒输出一次统计信息", intervalSeconds);
    }

    /**
     * 停止监控服务
     */
    private void stopMonitoring() {
        if (monitoringExecutor != null) {
            logger.info("停止监控服务...");
            monitoringExecutor.shutdown();
            ThreadPoolPerformanceAnalyzer.getInstance().stopMonitoring();
            logger.info("监控服务已停止");
        }
    }

    /**
     * 记录系统指标
     */
    private void logSystemMetrics() {
        try {
            // 记录基本的系统指标
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            long maxMemory = runtime.maxMemory();

            logger.info("系统内存: 已用={}MB, 总计={}MB, 最大={}MB, 使用率={}%",
                    usedMemory / 1024 / 1024, totalMemory / 1024 / 1024, maxMemory / 1024 / 1024,
                    String.format("%.2f", usedMemory * 100.0 / maxMemory));

            // 检查是否需要垃圾回收
            double memoryUsageRatio = (double) usedMemory / maxMemory;
            if (memoryUsageRatio > 0.8) { // 使用固定阈值80%
                logger.info("内存使用率过高({}%)，建议执行垃圾回收", String.format("%.2f", memoryUsageRatio * 100));
                System.gc();
            }
        } catch (Exception e) {
            logger.warn("记录系统指标时发生异常", e);
        }
    }

    /**
     * 记录服务器统计信息
     */
    private void logServerStatistics() {
        try {
            // 记录Inbound服务器统计
            InboundServerManager.ManagerStatistics managerStats = inboundServerManager.getStatistics();
            logger.info("Inbound服务器统计: {}", managerStats);

            // 记录路由统计
            logger.info("路由统计: {}", router.getStatistics());

            // 记录代理处理器统计
            logger.info("活跃连接数: {}", proxyProcessor.getActiveConnectionCount());

        } catch (Exception e) {
            logger.warn("记录服务器统计信息时发生异常", e);
        }
    }

    /**
     * 执行健康检查
     */
    private void performHealthCheck() {
        try {
            InboundServerManager.HealthReport healthReport = inboundServerManager.getHealthReport();
            if (!healthReport.isAllHealthy()) {
                logger.warn("部分服务器不健康: {}", healthReport);
            }

            // 检查Outbound健康状态
            String defaultOutbound = config.getRouting().getDefaultOutbound();
            if (proxyProcessor.getOutboundHandler(defaultOutbound) != null) {
                proxyProcessor.getOutboundHandler(defaultOutbound).healthCheck()
                        .whenComplete((status, throwable) -> {
                            if (throwable != null) {
                                logger.warn("Outbound健康检查失败: {}", defaultOutbound, throwable);
                            } else if (status != com.xiang.proxy.server.outbound.OutboundHandler.HealthStatus.HEALTHY) {
                                logger.warn("Outbound健康状态异常: {} - {}", defaultOutbound, status);
                            }
                        });
            }

        } catch (Exception e) {
            logger.warn("执行健康检查时发生异常", e);
        }
    }

    /**
     * 注册关闭钩子
     */
    private void registerShutdownHook() {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            logger.info("收到关闭信号，正在优雅关闭代理服务器V2...");
            try {
                stop().get(15, TimeUnit.SECONDS);
                logger.info("代理服务器V2优雅关闭完成");
            } catch (Exception e) {
                logger.error("优雅关闭代理服务器V2时发生异常", e);
            }
        }, "ProxyServerV2-Shutdown"));
    }

    // 性能优化计算方法（从原ProxyServer移植）

    private int calculateOptimalBossThreads(int configuredThreads) {
        if (configuredThreads > 0) {
            return configuredThreads;
        }

        int cpuCores = Runtime.getRuntime().availableProcessors();
        return cpuCores >= 16 ? 2 : 1;
    }

    private int calculateOptimalWorkerThreads(int configuredThreads) {
        if (configuredThreads > 0) {
            return configuredThreads;
        }

        int cpuCores = Runtime.getRuntime().availableProcessors();
        return Math.max(4, cpuCores * 2);
    }

    private int calculateOptimalBacklog() {
        int cpuCores = Runtime.getRuntime().availableProcessors();
        int baseBacklog = Math.max(1024, cpuCores * 256);
        return Math.min(baseBacklog, 8192);
    }

    private int calculateOptimalBufferSize() {
        long maxMemory = Runtime.getRuntime().maxMemory();
        long availableMemory = maxMemory / (1024 * 1024); // MB

        if (availableMemory > 4096) { // > 4GB
            return 128 * 1024; // 128KB
        } else if (availableMemory > 2048) { // > 2GB
            return 96 * 1024;  // 96KB
        } else if (availableMemory > 1024) { // > 1GB
            return 64 * 1024;  // 64KB
        } else {
            return 32 * 1024;  // 32KB
        }
    }

    // Getter方法

    public Router getRouter() {
        return router;
    }

    public ProxyProcessor getProxyProcessor() {
        return proxyProcessor;
    }

    public InboundServerManager getInboundServerManager() {
        return inboundServerManager;
    }

    public boolean isRunning() {
        return running;
    }

    // 主方法

    public static void main(String[] args) {
        try {
            // 解析命令行参数
            parseCommandLineArgs(args);

            // 创建并启动代理服务器V2
            ProxyServerV2 server = new ProxyServerV2();
            server.start().get();

            logger.info("代理服务器V2启动成功，按Ctrl+C停止服务器");

            // 保持主线程运行
            Thread.currentThread().join();

        } catch (Exception e) {
            logger.error("代理服务器V2启动失败", e);
            System.exit(1);
        }
    }

    /**
     * 解析命令行参数
     */
    public static void parseCommandLineArgs(String[] args) {
        for (int i = 0; i < args.length; i++) {
            String arg = args[i];

            if (arg.startsWith("--config=")) {
                String configPath = arg.substring("--config=".length());
                ProxyServerV2ConfigManager.setConfigFilePath(configPath);
                continue;
            }

            if (arg.equals("-c") && i + 1 < args.length) {
                String configPath = args[i + 1];
                ProxyServerV2ConfigManager.setConfigFilePath(configPath);
                i++;
                continue;
            }

            if (arg.equals("--port") && i + 1 < args.length) {
                try {
                    int port = Integer.parseInt(args[i + 1]);
                    System.setProperty("proxy.server.port", String.valueOf(port));
                    i++;
                } catch (NumberFormatException e) {
                    logger.warn("无效的端口号: {}", args[i + 1]);
                }
            }
        }
    }

    /**
     * 获取配置
     */
    public ProxyServerV2Properties getConfig() {
        return config;
    }

    // 安全的类型转换辅助方法

    private int getIntValue(ProxyServerV2Properties.OutboundProperties properties, String key, int defaultValue) {
        Object value = properties.getConfigValue(key, defaultValue);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return defaultValue;
    }

    private long getLongValue(ProxyServerV2Properties.OutboundProperties properties, String key, long defaultValue) {
        Object value = properties.getConfigValue(key, defaultValue);
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        return defaultValue;
    }

    private boolean getBooleanValue(ProxyServerV2Properties.OutboundProperties properties, String key, boolean defaultValue) {
        Object value = properties.getConfigValue(key, defaultValue);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        return defaultValue;
    }

    private int getIntValue(ProxyServerV2Properties.InboundProperties properties, String key, int defaultValue) {
        Object value = properties.getConfigValue(key, defaultValue);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return defaultValue;
    }

    private boolean getBooleanValue(ProxyServerV2Properties.InboundProperties properties, String key, boolean defaultValue) {
        Object value = properties.getConfigValue(key, defaultValue);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        return defaultValue;
    }

    private String getStringValue(ProxyServerV2Properties.InboundProperties properties, String key, String defaultValue) {
        Object value = properties.getConfigValue(key, defaultValue);
        if (value instanceof String) {
            return (String) value;
        }
        return defaultValue;
    }
}