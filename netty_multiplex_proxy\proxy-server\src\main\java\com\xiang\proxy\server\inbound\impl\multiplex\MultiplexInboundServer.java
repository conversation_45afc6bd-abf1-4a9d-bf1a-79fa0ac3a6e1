package com.xiang.proxy.server.inbound.impl.multiplex;

import com.xiang.proxy.server.core.ProxyProcessor;
import com.xiang.proxy.server.inbound.AbstractInboundServer;
import com.xiang.proxy.server.inbound.InboundServerConfig;
import com.xiang.proxy.server.protocol.MultiplexProtocol;
import com.xiang.proxy.server.ssl.SslContextManager;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.codec.ByteToMessageDecoder;
import io.netty.handler.ssl.SslHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLEngine;
import java.util.List;

/**
 * 多路复用Inbound服务器
 * 专门处理多路复用协议的Netty服务器
 */
public class MultiplexInboundServer extends AbstractInboundServer {
    private static final Logger logger = LoggerFactory.getLogger(MultiplexInboundServer.class);

    private final MultiplexInboundHandler multiplexHandler;

    public MultiplexInboundServer(String serverId, InboundServerConfig config, ProxyProcessor proxyProcessor) {
        super(serverId, config, proxyProcessor);
        this.multiplexHandler = new MultiplexInboundHandler(proxyProcessor);
    }

    @Override
    public String getServerType() {
        return "multiplex";
    }

    @Override
    public ChannelInitializer<SocketChannel> createChannelInitializer() {
        return new ChannelInitializer<SocketChannel>() {
            @Override
            protected void initChannel(SocketChannel ch) throws Exception {
                ChannelPipeline pipeline = ch.pipeline();

                // 如果启用了SSL，添加SSL处理器
                if (config.isEnableSsl()) {
                    if (sslContext != null) {
                        SSLEngine sslEngine = sslContext.newEngine(ch.alloc());

                        // 配置SSL引擎
                        configureSslEngine(sslEngine);

                        SslHandler sslHandler = new SslHandler(sslEngine);

                        // 设置握手超时
                        int handshakeTimeout = SslContextManager.getInstance().getHandshakeTimeoutSeconds();
                        sslHandler.setHandshakeTimeoutMillis(handshakeTimeout * 1000L);

                        pipeline.addLast("ssl", sslHandler);
                        logger.debug("为客户端连接添加SSL处理器: {}", ch.remoteAddress());
                    }
                }

                // 连接管理处理器
                pipeline.addLast("connection", createConnectionHandler());

                // 添加协议检测器
                pipeline.addLast("protocolDetector", new MultiplexProtocolDetector());

                logger.debug("初始化多路复用连接管道: {}", ch.remoteAddress());
            }
        };
    }

    /**
     * 配置SSL引擎
     */
    private void configureSslEngine(SSLEngine sslEngine) {
        // 设置为服务器模式
        sslEngine.setUseClientMode(false);

        // 根据配置设置客户端认证
        // 这些配置会在SslContextManager中处理，这里主要是确保引擎配置正确
        logger.debug("SSL引擎配置完成，协议: {}, 密码套件数量: {}",
                sslEngine.getEnabledProtocols().length > 0 ? sslEngine.getEnabledProtocols()[0] : "default",
                sslEngine.getEnabledCipherSuites().length);
    }

    /**
     * 多路复用通道处理器
     * 将Netty的ChannelHandler适配到MultiplexInboundHandler
     */
    private class MultiplexChannelHandler extends io.netty.channel.ChannelInboundHandlerAdapter {
        @Override
        public void channelActive(io.netty.channel.ChannelHandlerContext ctx) throws Exception {
            logger.debug("多路复用连接激活: {}", ctx.channel().remoteAddress());
            super.channelActive(ctx);
        }

        @Override
        public void channelRead(io.netty.channel.ChannelHandlerContext ctx, Object msg) throws Exception {
            try {
                // 更新统计信息
                if (msg instanceof ByteBuf) {
                   ByteBuf buf = (ByteBuf) msg;
                    statistics.addBytesReceived(buf.readableBytes());
                    statistics.incrementTotalRequests();
                }

                // 委托给MultiplexInboundHandler处理
                multiplexHandler.handleInbound(ctx.channel(), msg);

            } catch (Exception e) {
                logger.error("处理多路复用数据时发生异常: {}", ctx.channel().remoteAddress(), e);
                statistics.incrementRequestErrors();
                ctx.close();
            }
        }

        @Override
        public void channelInactive(io.netty.channel.ChannelHandlerContext ctx) throws Exception {
            logger.debug("多路复用连接断开: {}", ctx.channel().remoteAddress());
            // 通知处理器进行会话清理，确保后端连接按策略归还连接池
            multiplexHandler.onClientDisconnected(ctx.channel());
            super.channelInactive(ctx);
        }

        @Override
        public void exceptionCaught(io.netty.channel.ChannelHandlerContext ctx, Throwable cause) throws Exception {
            logger.error("多路复用连接异常: {}", ctx.channel().remoteAddress(), cause);
            statistics.incrementConnectionErrors();
            ctx.close();
        }
    }

    /**
     * 协议检测器
     * 检测连接使用的协议类型（HTTP代理 vs 多路复用协议）
     */
    private class MultiplexProtocolDetector extends ByteToMessageDecoder {
        private static final Logger logger = LoggerFactory.getLogger(com.xiang.proxy.server.protocol.MultiplexProtocolDetector.class);

        @Override
        protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) throws Exception {
            if (in.readableBytes() < 2) {
                // 需要至少2个字节来检测协议
                logger.debug("数据不足，等待更多数据进行协议检测: {} bytes", in.readableBytes());
                return;
            }

            // 标记当前读取位置
            in.markReaderIndex();

            // 读取前两个字节检查是否为多路复用协议魔数
            short magic = in.readShort();

            // 重置读取位置
            in.resetReaderIndex();

            logger.debug("协议检测: magic=0x{}, 期望=0x{}", Integer.toHexString(magic & 0xFFFF), Integer.toHexString(MultiplexProtocol.MAGIC & 0xFFFF));

            if (magic == MultiplexProtocol.MAGIC) {
                // 多路复用协议
                logger.info("检测到多路复用协议连接: {}", ctx.channel().remoteAddress());

                // 移除协议检测器，添加多路复用处理器
                ctx.pipeline().remove(this);
                MultiplexChannelHandler multiplexHandler = new MultiplexChannelHandler();
                ctx.pipeline().addLast("multiplexHandler", multiplexHandler);

                // 手动触发channelActive事件，因为新添加的处理器错过了这个事件
                multiplexHandler.channelActive(ctx);

                // 将缓冲区中的所有数据传递给新的处理器
                if (in.isReadable()) {
                    ByteBuf remainingData = in.readBytes(in.readableBytes());
                    // 直接调用新处理器的channelRead方法处理剩余数据
                    multiplexHandler.channelRead(ctx, remainingData);
                }
            } else {
                // 不是多路复用协议，将数据传递给下一个处理器
                if (in.isReadable()) {
                    out.add(in.readBytes(in.readableBytes()));
                }
            }
        }
    }

    /**
     * 获取多路复用处理器
     */
    public MultiplexInboundHandler getMultiplexHandler() {
        return multiplexHandler;
    }

    /**
     * 获取多路复用特定的统计信息
     */
    public MultiplexStatistics getMultiplexStatistics() {
        return new MultiplexStatistics(
                multiplexHandler.getTotalSessions(),
                multiplexHandler.getTotalActiveSubSessions(),
                statistics.getTotalRequests(),
                statistics.getCurrentConnections()
        );
    }

    /**
     * 多路复用统计信息
     */
    public static class MultiplexStatistics {
        private final int totalSessions;
        private final int activeSubSessions;
        private final long totalRequests;
        private final long currentConnections;

        public MultiplexStatistics(int totalSessions, int activeSubSessions,
                                   long totalRequests, long currentConnections) {
            this.totalSessions = totalSessions;
            this.activeSubSessions = activeSubSessions;
            this.totalRequests = totalRequests;
            this.currentConnections = currentConnections;
        }

        public int getTotalSessions() {
            return totalSessions;
        }

        public int getActiveSubSessions() {
            return activeSubSessions;
        }

        public long getTotalRequests() {
            return totalRequests;
        }

        public long getCurrentConnections() {
            return currentConnections;
        }

        public double getAverageSubSessionsPerConnection() {
            return currentConnections > 0 ? (double) activeSubSessions / currentConnections : 0.0;
        }

        @Override
        public String toString() {
            return String.format("MultiplexStatistics{sessions=%d, subSessions=%d, " +
                            "requests=%d, connections=%d, avgSubSessions=%.2f}",
                    totalSessions, activeSubSessions, totalRequests, currentConnections,
                    getAverageSubSessionsPerConnection());
        }
    }
}