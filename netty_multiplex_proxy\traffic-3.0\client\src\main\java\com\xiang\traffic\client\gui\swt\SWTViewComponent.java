package com.xiang.traffic.client.gui.swt;

import com.xiang.traffic.AbstractComponent;
import com.xiang.traffic.ConfigManager;
import com.xiang.traffic.client.Client;
import org.eclipse.swt.widgets.Display;

import java.util.Objects;

/**
 * SWT GUI组件
 *
 * <AUTHOR>
 * @date 2024/6/6 15:02
 */
public class SWTViewComponent extends AbstractComponent<Client> {

    private final Display display;

    public SWTViewComponent(Client parent) {
        super("SWTViewComponent", Objects.requireNonNull(parent));

        ConfigManager<?> configManager = getConfigManager();
        if (configManager.isMacOS()) {
            configManager.setSystemProperties("apple.awt.UIElement", "true");
        }

        try {
            this.display = Display.getDefault();
        } catch (Throwable e) {
            if (Utils.isMacOS()) {
                log.warn("Please use VM argument -XstartOnFirstThread on MacOS");
            }
            throw new Error(e);
        }
    }

    @Override
    protected void initInternal() {
        try {
            addModule(new TrayModule(this));
            addModule(new ServerSettingModule(this));
            addModule(new SocksSettingModule(this));
            addModule(new MainScreenModule(this));
            addModule(new HttpProxySettingModule(this));
        } catch (Throwable t) {
            log.error("SWT Thread occur a error", t);
            Client.exitWithNotify(1, "exitmsg.swt_view.init_failure", t.getMessage());
        }
    }

    @Override
    protected void startInternal() {
        parent.setGUITask(() -> {
            try {
                Thread t = Thread.currentThread();
                t.setName("SWT-UI-Thread");
                while (!t.isInterrupted()) {
                    if (!display.readAndDispatch()) {
                        display.sleep();
                    }
                }
                display.dispose();
            } catch (RuntimeException | Error t) {
                log.error("An error occur in SWT-UI-Thread", t);
                Client.exitWithNotify(1, "exitmsg.swt_view.run_error", t.getMessage());
            }
        });
    }

    Display getDisplay() {
        return display;
    }

    void openSocksSettingUI() {
        getModuleByName(SocksSettingModule.NAME, SocksSettingModule.class).setVisiable(true);
    }

    void openServerSettingUI() {
        getModuleByName(ServerSettingModule.NAME, ServerSettingModule.class).setVisiable(true);
    }

    void openMainScreenUI() {
        getModuleByName(MainScreenModule.NAME, MainScreenModule.class).setVisiable(true);
    }

    void openHttpProxySettingUI() {
        getModuleByName(HttpProxySettingModule.NAME, HttpProxySettingModule.class).setVisiable(true);
    }

    @Override
    protected void restartInternal() {
        throw new UnsupportedOperationException("Can not restart SWT View Component");
    }
}
