swtui.tray.item.open_main_screen_ui = \u6253\u5F00\u4E3B\u754C\u9762
swtui.tray.item.server_config_ui = \u7F16\u8F91\u670D\u52A1\u5668\u914D\u7F6E...
swtui.tray.item.socks5_config_ui = \u672C\u5730Socks5\u4EE3\u7406\u8BBE\u7F6E...
swtui.tray.item.http_config_ui = \u672C\u5730HTTP\u4EE3\u7406\u8BBE\u7F6E...
swtui.tray.item.exit = \u9000\u51FA
swtui.tray.item.proxy_mode = \u4EE3\u7406\u6A21\u5F0F
swtui.tray.item.proxy_mode.no_proxy = \u4E0D\u4EE3\u7406
swtui.tray.item.proxy_mode.gfwlist = GFW List\u6A21\u5F0F
swtui.tray.item.proxy_mode.ipwhitelist = \u4EC5\u4EE3\u7406\u5883\u5916\u5730\u5740
swtui.tray.item.proxy_mode.global = \u5168\u5C40\u4EE3\u7406
swtui.tray.item.help = \u5E2E\u52A9/\u5173\u4E8E
swtui.tray.item.help.open_config_dir = \u6253\u5F00\u914D\u7F6E\u6587\u4EF6\u76EE\u5F55
swtui.tray.item.help.open_log_dir = \u6253\u5F00\u65E5\u5FD7\u6587\u4EF6\u76EE\u5F55
swtui.tray.item.help.clean_log = \u6E05\u7A7A\u65E5\u5FD7
swtui.tray.item.help.open_github = GitHub\u9875\u9762
swtui.tray.item.help.open_issue = \u95EE\u9898\u53CD\u9988

swtui.main.title = \u4E3B\u754C\u9762
swtui.main.serverlist.label = \u9009\u62E9\u670D\u52A1\u5668
swtui.main.status.not_connect = \u672A\u8FDE\u63A5
swtui.main.status.new = \u521D\u59CB\u5316\u4E2D...
swtui.main.status.ssl_initial = \u51C6\u5907SSL\u8BC1\u4E66\u8FDE\u63A5...
swtui.main.status.ssl_connecting = \u6B63\u5728\u53D1\u8D77SSL\u8BC1\u4E66\u8FDE\u63A5...
swtui.main.status.ssl_connect_timeout = SSL\u8BC1\u4E66\u8FDE\u63A5\u8D85\u65F6,\u8BF7\u68C0\u67E5\u670D\u52A1\u5668\u914D\u7F6E
swtui.main.status.ssl_connect_auth_failure = \u672A\u901A\u8FC7\u670D\u52A1\u5668\u8BA4\u8BC1,\u8BF7\u68C0\u67E5\u8BA4\u8BC1\u4FE1\u606F\u662F\u5426\u6B63\u786E
swtui.main.status.ssl_connect = \u6B63\u5728\u83B7\u53D6SSL\u8BC1\u4E66...
swtui.main.status.ssl_connect_done = SSL\u8BC1\u4E66\u83B7\u53D6\u5B8C\u6210
swtui.main.status.ssl_connect_error = SSL\u8BC1\u4E66\u83B7\u53D6\u9519\u8BEF
swtui.main.status.proxy_initial = \u51C6\u5907\u53D1\u8D77\u4EE3\u7406\u8FDE\u63A5...
swtui.main.status.proxy_connecting = \u6B63\u5728\u8FDE\u63A5\u4EE3\u7406\u670D\u52A1...
swtui.main.status.proxy_connect_timeout = \u4EE3\u7406\u670D\u52A1\u8FDE\u63A5\u8D85\u65F6
swtui.main.status.proxy_connect = \u6210\u529F\u4E0E\u670D\u52A1\u5668\u5EFA\u7ACB\u4EE3\u7406\u670D\u52A1\u8FDE\u63A5
swtui.main.status.proxy_connect_auth_failure = \u4EE3\u7406\u670D\u52A1\u8BA4\u8BC1\u5931\u8D25,\u8BF7\u68C0\u67E5\u8BA4\u8BC1\u4FE1\u606F\u662F\u5426\u6B63\u786E
swtui.main.status.proxy_connect_error = \u4E0E\u4EE3\u7406\u670D\u52A1\u8FDE\u63A5\u53D1\u751F\u9519\u8BEF
swtui.main.status.proxy_disconnect = \u6682\u65F6\u4E0E\u670D\u52A1\u5668\u65AD\u5F00\u8FDE\u63A5,\u5C1D\u8BD5\u8FDB\u884C\u91CD\u8FDE...
swtui.main.status.proxy_unused = \u4EE3\u7406\u670D\u52A1\u5668\u8FDE\u63A5\u5DF2\u505C\u6B62
swtui.main.button.connect = \u8FDE\u63A5
swtui.main.button.disconnect = \u65AD\u5F00\u8FDE\u63A5
swtui.main.notice.title = \u63D0\u793A
swtui.main.notice.server_not_select = \u8BF7\u9009\u62E9\u4E00\u4E2A\u6709\u6548\u7684\u670D\u52A1\u5668

swtui.serverconfig.title = \u670D\u52A1\u5668\u8BBE\u7F6E
swtui.serverconfig.list.title = \u670D\u52A1\u5668\u5217\u8868
swtui.serverconfig.list.first = \u70B9\u51FB\u6B64\u5904\u8FDB\u884C\u6DFB\u52A0
swtui.serverconfig.form.label.address = \u5730\u5740
swtui.serverconfig.form.label.port = \u7AEF\u53E3
swtui.serverconfig.form.label.ssl_port = \u8BC1\u4E66\u7AEF\u53E3
swtui.serverconfig.form.label.encrypt_type = \u52A0\u5BC6\u65B9\u5F0F
swtui.serverconfig.form.label.auth_type = \u8BA4\u8BC1\u65B9\u5F0F
swtui.serverconfig.form.label.username = \u7528\u6237\u540D
swtui.serverconfig.form.label.password = \u5BC6\u7801
swtui.serverconfig.form.encrypt.none = \u65E0\u52A0\u5BC6
swtui.serverconfig.form.encrypt.ssl = TLS v1.2
swtui.serverconfig.form.encrypt.ssl_ca = TLS v1.2 (CA\u8BC1\u4E66)
swtui.serverconfig.form.auth.normal = \u666E\u901A\u8BA4\u8BC1
swtui.serverconfig.form.auth.user = \u7528\u6237\u8BA4\u8BC1
swtui.serverconfig.form.save = \u4FDD\u5B58
swtui.serverconfig.form.delete = \u5220\u9664
swtui.serverconfig.notice.error.title = \u9519\u8BEF
swtui.serverconfig.notice.error.host_error = \u670D\u52A1\u5668\u4E3B\u673A\u540D\u683C\u5F0F\u6709\u8BEF:\u9700\u8981\u4E3AIPv4\u5730\u5740\u6216\u662F\u5408\u6CD5\u4E3B\u673A\u540D/\u57DF\u540D
swtui.serverconfig.notice.error.port_error = \u7AEF\u53E3\u53F7\u6709\u8BEF,\u5FC5\u987B\u4E3A1~65535\u4E4B\u95F4\u7684\u6570\u5B57
swtui.serverconfig.notice.error.ssl_port_error = \u8BC1\u4E66\u7AEF\u53E3\u53F7\u6709\u8BEF,\u5FC5\u987B\u4E3A1~65535\u4E4B\u95F4\u7684\u6570\u5B57
swtui.serverconfig.notice.info.title = \u63D0\u793A
swtui.serverconfig.notice.info.server_exists = \u5DF2\u7ECF\u5305\u542B\u670D\u52A1\u5668 {0}:{1} \u914D\u7F6E
swtui.serverconfig.notice.info.no_server_select = \u8BF7\u9009\u62E9\u9700\u8981\u5220\u9664\u7684\u670D\u52A1\u5668\u914D\u7F6E
swtui.serverconfig.notice.success.title = \u6210\u529F
swtui.serverconfig.notice.success.server_added = \u6210\u529F\u6DFB\u52A0\u670D\u52A1\u5668\u914D\u7F6E {0}:{1}
swtui.serverconfig.notice.success.server_updated = \u6210\u529F\u4FEE\u6539\u670D\u52A1\u5668\u914D\u7F6E {0}:{1}

swtui.socks5.title = Socks5\u672C\u5730\u4EE3\u7406\u8BBE\u7F6E
swtui.socks5.form.label.validate = \u9A8C\u8BC1
swtui.socks5.form.label.username = \u7528\u6237\u540D
swtui.socks5.form.label.password = \u5BC6\u7801
swtui.socks5.form.label.port = \u4EE3\u7406\u7AEF\u53E3
swtui.socks5.form.button.open = \u6253\u5F00
swtui.socks5.form.button.close = \u5173\u95ED
swtui.socks5.form.button.enter = \u786E\u8BA4
swtui.socks5.form.button.cancel = \u53D6\u6D88
swtui.socks5.notice.title = \u63D0\u793A
swtui.socks5.notice.port_error = \u7AEF\u53E3\u4E0D\u5408\u6CD5
swtui.socks5.notice.update_success = \u4FEE\u6539\u5B8C\u6210, \u4EE3\u7406\u7AEF\u53E3\u7684\u4FEE\u6539\u9700\u8981\u91CD\u542F\u624D\u53EF\u751F\u6548
swtui.socks5.notice.unchanged = \u4FEE\u6539\u5B8C\u6210

swtui.http.title = HTTP\u4EE3\u7406\u8BBE\u7F6E
swtui.http.form.label.switch = \u5F00\u5173
swtui.http.form.label.port = \u4EE3\u7406\u7AEF\u53E3
swtui.http.form.label.validate = \u8BA4\u8BC1
swtui.http.form.label.username = \u7528\u6237\u540D
swtui.http.form.label.password = \u5BC6\u7801
swtui.http.form.button.switch_open = \u5F00\u542F
swtui.http.form.button.switch_close = \u5173\u95ED
swtui.http.form.button.validate_open = \u5F00\u542F
swtui.http.form.button.validate_close = \u5173\u95ED
swtui.http.form.button.enter = \u786E\u8BA4
swtui.http.form.button.cancel = \u53D6\u6D88
swtui.http.notice.error.title = \u9519\u8BEF
swtui.http.notice.error.port_error = \u7AEF\u53E3\u53F7\u6709\u8BEF,\u5FC5\u987B\u4E3A1~65535\u4E4B\u95F4\u7684\u6570\u5B57
swtui.http.notice.error.auth_error = \u8BA4\u8BC1\u5F00\u542F\u65F6\uFF0C\u7528\u6237\u540D\u548C\u5BC6\u7801\u4E0D\u5F97\u4E3A\u7A7A
swtui.http.notice.update_success = \u4FEE\u6539\u6210\u529F\uFF0C\u90E8\u5206\u8BBE\u7F6E\u9700\u8981\u91CD\u542F\u7A0B\u5E8F\u751F\u6548
swtui.http.form.button.wsp_open=\u5F00\u542F
swtui.http.form.button.wsp_close=\u5173\u95ED
swtui.tray.item.wsp_proxy=Windows\u7CFB\u7EDF\u4EE3\u7406
swtui.main.status.proxy_request_disconnect=\u7528\u6237\u5728\u522b\u5904\u767b\u9646，\u65ad\u5f00\u8fde\u63a5!
swtui.main.status.user_traffic_exhausted=\u7528\u6237\u6d41\u91cf\u5df2\u7528\u5b8c\uff0c\u65ad\u5f00\u8fde\u63a5!
