package com.xiang.traffic.client.proxy.transparent;

import com.xiang.traffic.AbstractConfig;
import com.xiang.traffic.ConfigInitializationException;
import com.xiang.traffic.ConfigManager;
import com.xiang.traffic.client.GlobalConfig;

import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Objects;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2024/6/6 15:14
 */
public class TransparentProxyConfig extends AbstractConfig {

    public static final String NAME = "config.transparent";

    private static final int DEFAULT_PORT = 6870;

    private final Path configPath;

    private int port;

    public TransparentProxyConfig(ConfigManager<?> configManager) {
        super(Objects.requireNonNull(configManager), NAME);
        GlobalConfig gc = configManager.getConfig(GlobalConfig.NAME, GlobalConfig.class);
        this.configPath = gc.configPath().resolve("transparent-option");
    }

    @Override
    protected void initInternal() throws ConfigInitializationException {
        createFileIfNotExists();
        loadFile();
    }


    public int getBindPort() {
        return port;
    }


    private void loadFile() {
        try (FileReader reader = new FileReader(configPath.toFile())) {
            Properties properties = new Properties();
            properties.load(reader);
            this.port = Integer.parseInt(properties.getProperty("port", Integer.toString(DEFAULT_PORT)));
        } catch (IOException e) {
            throw new ConfigInitializationException(e);
        }
    }


    private void createFileIfNotExists() {
        if (Files.isRegularFile(configPath)) {
            return;
        }

        if (Files.isDirectory(configPath)) {
            throw new ConfigInitializationException("Config [" + configPath + "] is Directory!");
        }

        Properties properties = new Properties();
        properties.put("port", Integer.toString(DEFAULT_PORT));

        try (FileWriter writer = new FileWriter(configPath.toFile())) {
            properties.store(writer, "flyingsocks transparent configuration (ONLY LINUX)");
        } catch (IOException e) {
            throw new ConfigInitializationException(e);
        }
    }


    @Override
    public void save() throws Exception {
        Properties properties = new Properties();
        properties.put("port", Integer.toString(this.port));

        removeAndCreateNewFile(configPath);

        try (FileWriter writer = new FileWriter(configPath.toFile())) {
            properties.store(writer, "flyingsocks transparent configuration (ONLY LINUX)");
        } catch (IOException e) {
            throw new ConfigInitializationException(e);
        }
    }

}
