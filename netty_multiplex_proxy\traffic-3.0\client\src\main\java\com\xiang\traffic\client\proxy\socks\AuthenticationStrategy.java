package com.xiang.traffic.client.proxy.socks;

import io.netty.channel.ChannelPipeline;
import org.apache.commons.lang3.StringUtils;

/**
 * Socks5 客户端认证策略
 *
 * <AUTHOR>
 * @since 2024/6/6 15:12
 */
interface AuthenticationStrategy {

    /**
     * 执行认证
     *
     * @param username 用户名
     * @param password 密码
     * @return 认证是否通过
     */
    boolean grantAuthorization(String username, String password);

    /**
     * 认证通过后对管道的操作
     * 目前预留，方便日后扩展
     *
     * @param pipeline 当前Socks5客户端连接管道对象
     * @param username 用户名
     */
    default void afterAuthorizationSuccess(ChannelPipeline pipeline, String username) {
    }

}


/**
 * 简易认证策略，使用固定的用户名和密码
 */
class SimpleAuthenticationStrategy implements AuthenticationStrategy {

    private final String username;

    private final String password;

    SimpleAuthenticationStrategy(String username, String password) {
        if (StringUtils.isAnyBlank(username, password)) {
            throw new IllegalArgumentException("Username or password is blank");
        }

        this.username = username;
        this.password = password;
    }

    @Override
    public boolean grantAuthorization(String username, String password) {
        return this.username.equals(username) && this.password.equals(password);
    }
}
