package com.xiang.traffic.server.core;


import com.xiang.traffic.AbstractConfig;
import com.xiang.traffic.ConfigInitializationException;
import com.xiang.traffic.ConfigManager;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;

public class OpenSSLConfig extends AbstractConfig {
    /**
     * 默认配置名前缀
     */
    private static final String NAME_PREFIX = "config.OpenSSL";

    /**
     * 证书文件URL,如果是PEM格式直接重命名为CRT格式
     */
    private final URL rootCertURL;

    /**
     * PCKS8编码的私钥文件
     */
    private final URL privateKeyURL;

    public OpenSSLConfig(ConfigManager<?> configManager, String nodeName) {
        super(configManager, configName(nodeName));
        try {
            this.rootCertURL = new URL(String.format("classpath://encrypt/%s/ca.crt", nodeName));
            this.privateKeyURL = new URL(String.format("classpath://encrypt/%s/private.key", nodeName));
        } catch (Exception e) {
            throw new ConfigInitializationException(e);
        }
    }

    /**
     * @return 打开一个私钥文件输入流，使用完成后请手动关闭
     */
    public InputStream openKeyStream() throws IOException {
        return privateKeyURL.openStream();
    }

    /**
     * @return 打开一个证书文件输入流，使用完成后请手动关闭
     */
    public InputStream openRootCertStream() throws IOException {
        return rootCertURL.openStream();
    }

    public static String configName(String nodeName) {
        return NAME_PREFIX + "." + nodeName;
    }
}
