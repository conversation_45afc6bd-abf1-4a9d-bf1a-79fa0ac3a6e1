package com.xiang.traffic.server;

import com.xiang.traffic.Component;
import com.xiang.traffic.ConfigManager;
import com.xiang.traffic.Environment;
import com.xiang.traffic.VoidComponent;
import com.xiang.traffic.server.db.user.UserDatabase;

public interface Server extends Component<VoidComponent>, Environment {

    /**
     * @return 服务器版本
     */
    String getVersion();

    /**
     * @return 服务器配置信息
     */
    ServerConfig config();

    /**
     * @return 服务器配置管理器
     */
    ConfigManager<?> getConfigManager();

    /**
     * @return 用户数据库
     */
    UserDatabase getUserDatabase();

}
