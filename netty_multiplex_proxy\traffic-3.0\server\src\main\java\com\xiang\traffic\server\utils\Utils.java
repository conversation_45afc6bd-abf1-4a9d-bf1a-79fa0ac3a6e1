package com.xiang.traffic.server.utils;

import com.xiang.traffic.server.enumeration.DbTypeEnum;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/2/7 0:56
 */
public class Utils {
    private static final String DB_TYPE_KEY = "flyingsocks.usermanage.dbType";

    private Utils() {
        throw new RuntimeException(Utils.class.getName() + "can not be instantiated!");
    }

    /**
     * @return
     */
    public static DbTypeEnum getDbTypeFromSystemProperty() {
        String dbTypeStr = System.getProperty(DB_TYPE_KEY);
        if (StringUtils.isEmpty(dbTypeStr)) {
            throw new IllegalArgumentException("flyingsocks.usermanage.dbType in config[config.properties] is not specified");
        }
        dbTypeStr = dbTypeStr.toUpperCase();
        return DbTypeEnum.valueOf(dbTypeStr);
    }
}
