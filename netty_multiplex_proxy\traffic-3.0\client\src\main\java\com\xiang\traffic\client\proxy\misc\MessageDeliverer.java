package com.xiang.traffic.client.proxy.misc;

import io.netty.buffer.ByteBuf;

import java.util.ArrayDeque;
import java.util.Objects;
import java.util.Queue;

/**
 * 消息传递工具
 *
 * <AUTHOR>
 * @date 2024/6/6 15:11
 */
public class MessageDeliverer {

    /**
     * MessageDeliverer是否被关闭
     */
    private volatile boolean cancel = false;

    /**
     * 消息接收者尚未设置时缓存 {@link ByteBuf}
     */
    private volatile Queue<ByteBuf> transferCache = new ArrayDeque<>();

    /**
     * 消息接收者
     */
    private volatile MessageReceiver messageReceiver;


    public void transfer(ByteBuf buf) throws MessageDelivererCancelledException {
        Objects.requireNonNull(buf);

        if (cancel) {
            throw new MessageDelivererCancelledException();
        }

        MessageReceiver receiver = this.messageReceiver;
        if (receiver != null) {
            receiver.receive(buf.retain());
            return;
        }

        synchronized (this) {
            if (cancel) {
                throw new MessageDelivererCancelledException();
            }

            receiver = this.messageReceiver;
            if (receiver == null) {
                transferCache.offer(buf.retain());
                return;
            }

            receiver.receive(buf.retain());
        }
    }


    public void setReceiver(MessageReceiver receiver) throws MessageDelivererCancelledException {
        if (messageReceiver != null) {
            throw new IllegalStateException("Receiver has set");
        }

        if (cancel) {
            throw new MessageDelivererCancelledException();
        }

        Objects.requireNonNull(receiver);
        synchronized (this) {
            if (cancel) {
                throw new MessageDelivererCancelledException();
            }

            Queue<ByteBuf> cache = this.transferCache;
            while (!cache.isEmpty()) {
                ByteBuf buf = cache.poll();
                receiver.receive(buf);
            }

            this.messageReceiver = receiver;
            this.transferCache = null;
        }
    }


    public void cancel() {
        if (cancel) {
            return;
        }

        synchronized (this) {
            cancel = true;
            MessageReceiver receiver = this.messageReceiver;
            if (receiver != null) {
                receiver.close();
            }

            Queue<ByteBuf> cache = this.transferCache;
            if (cache != null) {
                cache.forEach(ByteBuf::release);
                this.transferCache = null;
            }
        }
    }

    @Override
    protected void finalize() {
        Queue<ByteBuf> cache = this.transferCache;
        if (cache != null && !cache.isEmpty()) {
            cache.forEach(ByteBuf::release);
        }
    }
}
