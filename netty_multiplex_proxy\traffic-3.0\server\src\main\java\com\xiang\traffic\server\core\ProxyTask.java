package com.xiang.traffic.server.core;


import com.xiang.traffic.protocol.ProxyRequestMessage;

import java.util.Objects;

/**
 * 代理任务对象
 */
public class ProxyTask {

    private final ProxyRequestMessage proxyRequestMessage;

    private final ClientSession session;

    public ProxyTask(ProxyRequestMessage proxyRequestMessage, ClientSession clientSession) {
        this.proxyRequestMessage = Objects.requireNonNull(proxyRequestMessage, "ProxyRequestMessage must not be null");
        this.session = Objects.requireNonNull(clientSession, "ClientSession must not be null");
    }

    public ProxyRequestMessage getRequestMessage() {
        return proxyRequestMessage;
    }

    public ClientSession session() {
        return session;
    }

    @Override
    public int hashCode() {
        return session.hashCode() ^ proxyRequestMessage.getHost().hashCode() ^
                (proxyRequestMessage.getPort() << 16) ^ proxyRequestMessage.getProtocol().hashCode();
    }

    @Override
    public final boolean equals(Object obj) {
        return this == obj;
    }

    @Override
    public String toString() {
        return "ProxyTask{" +
                "proxyRequestMessage=" + proxyRequestMessage +
                ", session=" + session +
                '}';
    }
}
