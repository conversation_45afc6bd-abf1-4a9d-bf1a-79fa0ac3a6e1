package com.xiang.traffic.server.core.token;

import com.xiang.traffic.server.enumeration.ClientAuthType;

/**
 * <AUTHOR>
 * @date 2024/2/6 21:22
 */
public class SimpleToken extends AbstractAuthToken<IUser> {

    public SimpleToken(IUser principle, String remoteIp) {
        super(principle, remoteIp);
    }

    @Override
    public ClientAuthType getTokenType() {
        return ClientAuthType.SIMPLE;
    }
}
