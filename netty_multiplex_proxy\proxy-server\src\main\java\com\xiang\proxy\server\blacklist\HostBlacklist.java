package com.xiang.proxy.server.blacklist;

import com.xiang.proxy.server.cache.CacheManager;
import com.xiang.proxy.server.config.ProxyServerConfigManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.Map;
import java.util.LinkedHashMap;

/**
 * 主机黑名单管理器
 * 用于临时屏蔽无法连接的主机，避免重复尝试连接
 */
public class HostBlacklist {
    private static final Logger logger = LoggerFactory.getLogger(HostBlacklist.class);
    
    private static final HostBlacklist INSTANCE = new HostBlacklist();
    
    // 使用CacheManager管理黑名单缓存：主机 -> 加入黑名单的时间戳
    private final CacheManager<String, Long> blacklistCache;

    // 失败次数统计：主机 -> 失败次数
    private final ConcurrentHashMap<String, Integer> failureCount = new ConcurrentHashMap<>();

    // 黑名单命中统计 (主机名 -> 命中次数)
    private final ConcurrentHashMap<String, Long> blacklistHitCount = new ConcurrentHashMap<>();
    
    // 清理任务调度器
    private final ScheduledExecutorService cleanupExecutor;

    // 最大黑名单条目数
    public static final int MAX_BLACKLIST_SIZE = 1000;
    
    // 获取配置管理器
    private static ProxyServerConfigManager getConfigManager() {
        return ProxyServerConfigManager.getInstance();
    }

    // 获取失败阈值（从配置文件）
    private static int getFailureThreshold() {
        return getConfigManager().getBlacklistFailureThreshold();
    }

    // 获取黑名单缓存时间（从配置文件，转换为毫秒）
    private static long getBlacklistCacheTime() {
        return getConfigManager().getBlacklistCacheTimeoutSeconds() * 1000L;
    }
    
    private HostBlacklist() {
        // 初始化CacheManager管理黑名单缓存
        this.blacklistCache = new CacheManager<>("HostBlacklist", getBlacklistCacheTime(), MAX_BLACKLIST_SIZE);
        
        this.cleanupExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "HostBlacklist-Cleanup");
            t.setDaemon(true);
            return t;
        });
        
        // 启动定期清理任务
        startCleanupTask();

        logger.info("主机黑名单管理器已启动，启用状态: {}, 缓存时间: {}ms, 失败阈值: {}",
            getConfigManager().isBlacklistEnabled(), getBlacklistCacheTime(), getFailureThreshold());
    }
    
    public static HostBlacklist getInstance() {
        return INSTANCE;
    }
    
    /**
     * 检查主机是否在黑名单中
     */
    public boolean isBlacklisted(String host) {
        // 如果黑名单功能未启用，直接返回 false
        if (!getConfigManager().isBlacklistEnabled()) {
            return false;
        }

        Long blacklistTime = blacklistCache.get(host);
        if (blacklistTime == null) {
            return false;
        }

        // 统计黑名单命中次数
        blacklistHitCount.put(host, blacklistHitCount.getOrDefault(host, 0L) + 1);

        return true;
    }

    // 优化：缓存当前时间，减少系统调用
    private volatile long cachedCurrentTime = System.currentTimeMillis();
    private volatile long lastTimeUpdate = System.currentTimeMillis();

    private long getCurrentTime() {
        long now = System.currentTimeMillis();
        // 每100ms更新一次缓存时间
        if (now - lastTimeUpdate > 100) {
            cachedCurrentTime = now;
            lastTimeUpdate = now;
        }
        return cachedCurrentTime;
    }
    
    /**
     * 记录主机连接失败
     */
    public void recordFailure(String host) {
        // 如果黑名单功能未启用，不记录失败
        if (!getConfigManager().isBlacklistEnabled()) {
            return;
        }

        int failures = failureCount.getOrDefault(host, 0) + 1;
        failureCount.put(host, failures);

        logger.debug("主机 {} 连接失败，当前失败次数: {}", host, failures);

        // 如果失败次数达到阈值，加入黑名单
        if (failures >= getFailureThreshold()) {
            addToBlacklist(host);
        }
    }
    
    /**
     * 记录主机连接成功
     */
    public void recordSuccess(String host) {
        // 如果黑名单功能未启用，不需要清除记录
        if (!getConfigManager().isBlacklistEnabled()) {
            return;
        }

        // 连接成功，清除失败记录
        failureCount.remove(host);
        blacklistCache.remove(host);
        logger.debug("主机 {} 连接成功，已清除失败记录", host);
    }
    
    /**
     * 将主机加入黑名单
     */
    private void addToBlacklist(String host) {
        long currentTime = System.currentTimeMillis();
        blacklistCache.put(host, currentTime);
        
        logger.warn("主机 {} 已加入黑名单，缓存时间: {}分钟",
            host, getBlacklistCacheTime() / 60000);
    }
    
    /**
     * 启动定期清理任务
     */
    private void startCleanupTask() {
        cleanupExecutor.scheduleWithFixedDelay(() -> {
            // 只有在黑名单启用时才执行清理
            if (getConfigManager().isBlacklistEnabled()) {
                // CacheManager会自动清理过期条目，这里只需要记录统计信息
                try {
                    com.xiang.proxy.server.cache.CacheManager.CacheStats stats = blacklistCache.getStats();
                    logger.debug("黑名单缓存统计: 大小={}, 命中率={}% 淘汰数={}",
                        stats.size, String.format("%.2f", stats.getHitRate()), stats.evictionCount);
                } catch (Exception e) {
                    logger.debug("获取黑名单缓存统计信息失败: {}", e.getMessage());
                }
            }
        }, 60, 60, TimeUnit.SECONDS); // 每分钟记录一次统计信息
    }
    
    /**
     * 清理过期的黑名单条目
     */
    
    /**
     * 清理最旧的黑名单条目
     */
    
    /**
     * 获取黑名单统计信息（简单格式）
     */
    public String getStatsString() {
        return String.format("黑名单条目数: %d, 失败记录数: %d, 总命中数: %d",
            blacklistCache.getStats().size, failureCount.size(),
            blacklistHitCount.values().stream().mapToLong(Long::longValue).sum());
    }
    
    /**
     * 清空黑名单（用于测试或重置）
     */
    public void clear() {
        blacklistCache.clear();
        failureCount.clear();
        blacklistHitCount.clear();
        logger.info("黑名单已清空");
    }

    /**
     * 获取黑名单统计信息
     */
    public BlacklistStats getStats() {
        long totalHits = 0L;
        for (Long value : blacklistHitCount.values()) {
            totalHits += value;
        }

        // 获取命中次数最多的前5个主机
        Map<String, Long> topHitHosts = new LinkedHashMap<>();
        blacklistHitCount.entrySet().stream()
            .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
            .limit(5)
            .forEach(entry -> topHitHosts.put(entry.getKey(), entry.getValue()));

        return new BlacklistStats(
            blacklistCache.getStats().size,
            totalHits,
            topHitHosts
        );
    }
    
    /**
     * 关闭黑名单管理器
     */
    public void shutdown() {
        cleanupExecutor.shutdown();
        try {
            if (!cleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                cleanupExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            cleanupExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        blacklistCache.shutdown();
        logger.info("主机黑名单管理器已关闭");
    }

    /**
     * 黑名单统计信息类
     */
    public static class BlacklistStats {
        public final int totalBlacklistedHosts;
        public final long totalHits;
        public final Map<String, Long> topHitHosts;

        public BlacklistStats(int totalBlacklistedHosts, long totalHits, Map<String, Long> topHitHosts) {
            this.totalBlacklistedHosts = totalBlacklistedHosts;
            this.totalHits = totalHits;
            this.topHitHosts = topHitHosts;
        }

        @Override
        public String toString() {
            return String.format("BlacklistStats{hosts=%d, hits=%d, top=%s}",
                totalBlacklistedHosts, totalHits, topHitHosts);
        }
    }
}
