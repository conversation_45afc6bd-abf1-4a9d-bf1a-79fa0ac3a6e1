package com.xiang.traffic.server.core.client;

import com.xiang.traffic.AbstractComponent;
import com.xiang.traffic.Component;
import com.xiang.traffic.misc.FSMessageOutboundEncoder;
import com.xiang.traffic.misc.XorEncoderFactory;
import com.xiang.traffic.protocol.DnsMessage;
import com.xiang.traffic.protocol.*;
import com.xiang.traffic.server.ServerConfig;
import com.xiang.traffic.server.core.ClientSession;
import com.xiang.traffic.server.core.ProxyTask;
import com.xiang.traffic.server.core.ProxyTaskManager;
import com.xiang.traffic.server.db.user.MybaitsPlusUserDatabase;
import com.xiang.traffic.server.db.user.UserDatabase;
import io.netty.buffer.ByteBuf;
import io.netty.channel.AddressedEnvelope;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.channel.ChannelPipeline;
import io.netty.handler.codec.LengthFieldBasedFrameDecoder;
import io.netty.handler.codec.compression.JdkZlibDecoder;
import io.netty.handler.codec.compression.JdkZlibEncoder;
import io.netty.handler.codec.dns.*;
import io.netty.handler.timeout.IdleStateEvent;
import io.netty.handler.timeout.IdleStateHandler;
import io.netty.resolver.dns.DnsNameResolver;
import io.netty.util.ReferenceCountUtil;
import io.netty.util.concurrent.Future;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetSocketAddress;
import java.util.List;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/6/6 15:19
 */
class ProxyHandler extends ChannelInboundHandlerAdapter {

    public static final String HANDLER_NAME = "ProxyHandler";

    private static final String PROXY_REQUEST_FRAME_DECODER_NAME = "ProxyRequestFrameDecoder";
    private static final String XOR_DECODER_NAME = "XorDecoder";
    private static final String XOR_ENCODER_NAME = "XorEncoder";
    private static final int REQUEST_MAX_FRAME = 1024 * 1024 * 20;

    private static final Logger log = LoggerFactory.getLogger(ProxyHandler.class);
    private final AbstractComponent<ClientProcessor> component;

    private ClientSession clientSession;

    private ProxyTaskManager proxyTaskManager;

    private final ScheduledExecutorService scheduledTask = new ScheduledThreadPoolExecutor(10);

    public ProxyHandler(AbstractComponent<ClientProcessor> component) {
        this.component = component;
    }

    /**
     * IdleStateHandler -> [SslHandler] -> ClientSessionHandler -> FSMessageOutboundEncoder -> HeartbeatMessageHandler -> ProxyRequestFrameDecoder -> ProxyHandler
     */
    @Override
    public void handlerAdded(ChannelHandlerContext ctx) {
        ClientSession session = ConnectionContext.clientSession(ctx.channel());
        ProxyTaskManager manager = ConnectionContext.proxyTaskManager(ctx.channel());
        if (session == null || manager == null) {
            throw new IllegalStateException();
        }

        this.clientSession = session;
        this.proxyTaskManager = manager;

        ChannelPipeline cp = ctx.pipeline();
        cp.addFirst(new IdleStateHandler(20, 0, 0));
        cp.addBefore(HANDLER_NAME, PROXY_REQUEST_FRAME_DECODER_NAME, new LengthFieldBasedFrameDecoder(REQUEST_MAX_FRAME,
                ServiceStageMessage.LENGTH_FIELD_OFFSET, ServiceStageMessage.LENGTH_FIELD_SIZE, 0, 0));

        ServerConfig.Node node = this.component.getParentComponent().getParentComponent().getServerConfig();
        //增加异或编解码器
        if (node.xorEnable) {
            cp.addBefore(PROXY_REQUEST_FRAME_DECODER_NAME, XOR_DECODER_NAME, XorEncoderFactory.createXorDecoder());
            cp.addBefore(FSMessageOutboundEncoder.HANDLER_NAME, XOR_ENCODER_NAME, XorEncoderFactory.createXorEncoder());
        }

        //数据压缩
        if (node.zlibCompressEnable) {
            cp.addBefore(PROXY_REQUEST_FRAME_DECODER_NAME, null, new JdkZlibDecoder(node.zlibCompressAlgorithm));
            cp.addBefore(FSMessageOutboundEncoder.HANDLER_NAME, null, new JdkZlibEncoder(node.zlibCompressAlgorithm, node.zlibCompressLevel));
        }

        //检测用户流量是否超出限制
        checkUserTrafficRemainingCapacity(session);
    }

    /**
     * 检测用户流量是否超出限制
     *
     * @param session
     * @throws SerializationException
     */
    private void checkUserTrafficRemainingCapacity(ClientSession session) {
        Component<ClientProcessor> component = this.component;
        //TODO暂时这么处理，不用定时器会报错。
        scheduledTask.schedule(new Runnable() {
            @Override
            public void run() {
                //检测用户流量是否超出限制
                String username = session.getAuthToken().getPrinciple().getUsername();
                UserDatabase userDatabase = component.getParentComponent().getParentComponent().getParentComponent().getUserDatabase();
                if (userDatabase instanceof MybaitsPlusUserDatabase) {
                    MybaitsPlusUserDatabase mybaitsPlusUserDatabase = (MybaitsPlusUserDatabase) userDatabase;
                    long remainingCapacity = mybaitsPlusUserDatabase.getUserTrafficRemainingCapacity(username);
                    if (remainingCapacity <= 0) {
                        TrafficExhaustedMessage trafficExhaustedMessage = new TrafficExhaustedMessage();
                        session.socketChannel().writeAndFlush(trafficExhaustedMessage);
                    }
                }
            }
        }, 500, TimeUnit.MILLISECONDS);
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        if (msg instanceof ByteBuf) {
            try {
                ByteBuf buf = (ByteBuf) msg;

                byte serviceId = buf.getByte(buf.readerIndex());
                if (serviceId == ProxyResponseMessage.SERVICE_ID) {
                    processProxyRequestMessage(buf);
                } else if (serviceId == PingMessage.SERVICE_ID) {
                    new PingMessage(buf);
                    PongMessage pong = new PongMessage();
                    ctx.writeAndFlush(pong, ctx.voidPromise());
                } else if (serviceId == PongMessage.SERVICE_ID) {
                    new PongMessage(buf);
                } else if (serviceId == DnsMessage.SERVICE_ID) {
                    processDnsQueryMessage(ctx, buf);
                }
            } finally {
                ReferenceCountUtil.release(msg);
            }
        } else {
            ctx.fireChannelRead(msg);
        }
    }

    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) {
        if (evt instanceof IdleStateEvent) {
            PingMessage ping = new PingMessage();
            ctx.writeAndFlush(ping, ctx.voidPromise());
            return;
        }

        ctx.fireUserEventTriggered(evt);
    }

    protected void processProxyRequestMessage(ByteBuf buf) throws SerializationException {
        ProxyRequestMessage msg = new ProxyRequestMessage(buf);
        if (log.isDebugEnabled()) {
            log.debug("ProxyRequestMessage [{}:{}]", msg.getHost(), msg.getPort());
        }

        ProxyTask task = new ProxyTask(msg, clientSession);
        proxyTaskManager.publish(task);
    }


    protected void processDnsQueryMessage(ChannelHandlerContext ctx, ByteBuf buf) throws SerializationException {
        DnsNameResolver resolver = ConnectionContext.nameResolver(ctx.channel());

        DnsQueryMessage message = new DnsQueryMessage(buf);
        List<DnsMessage.Question> questions = message.getQuestions();

        // 暂时不处理QUESTION大于1的请求
        if (questions.size() > 1) {
            DnsResponseMessage msg = new DnsResponseMessage(message.getTransactionId());
            msg.setRCODE((byte) DnsResponseCode.FORMERR.intValue());
            ctx.writeAndFlush(msg);
            return;
        }

        DnsMessage.Question q = questions.get(0);
        DnsQuestion dnsq = new DefaultDnsQuestion(q.getName(), DnsRecordType.valueOf(q.getType()), q.getKlass());
        Future<AddressedEnvelope<DnsResponse, InetSocketAddress>> future = resolver.query(dnsq);
        future.addListener(f -> {
            if (!f.isSuccess()) {
                log.warn("DNS resolve failure, tid: {}", message.getTransactionId(), f.cause());
                DnsResponseMessage msg = new DnsResponseMessage(message.getTransactionId());
                msg.setRCODE((byte) DnsResponseCode.SERVFAIL.intValue());
                ctx.writeAndFlush(msg);
                return;
            }

            @SuppressWarnings("all")
            AddressedEnvelope<DnsResponse, InetSocketAddress> env = (AddressedEnvelope<DnsResponse, InetSocketAddress>) f.get();
            DnsResponse resp = env.content();
            DnsResponseCode code = resp.code();

            DnsResponseMessage msg = new DnsResponseMessage(message.getTransactionId());
            msg.setRCODE((byte) code.intValue());
            msg.setOpcode(resp.opCode().byteValue());
            msg.setTC(resp.isTruncated());
            msg.setRA(resp.isRecursionAvailable());
            msg.setRD(resp.isRecursionDesired());
            msg.setAA(resp.isAuthoritativeAnswer());

            int answerCnt = resp.count(DnsSection.ANSWER);
            for (int i = 0; i < answerCnt; i++) {
                DnsMessage.Record r = parseDnsRecord(resp.recordAt(DnsSection.ANSWER, i));
                msg.addAnswer(r);
            }

            int authorityCnt = resp.count(DnsSection.AUTHORITY);
            for (int i = 0; i < authorityCnt; i++) {
                DnsMessage.Record r = parseDnsRecord(resp.recordAt(DnsSection.AUTHORITY, i));
                msg.addAuthority(r);
            }

            int additionalCnt = resp.count(DnsSection.ADDITIONAL);
            for (int i = 0; i < additionalCnt; i++) {
                DnsMessage.Record r = parseDnsRecord(resp.recordAt(DnsSection.ADDITIONAL, i));
                msg.addAdditionalInfomation(r);
            }

            ctx.writeAndFlush(msg);
            env.release();
        });
    }


    protected static DnsMessage.Record parseDnsRecord(DnsRecord record) {
        int ttl = (int) record.timeToLive();
        int klass = record.dnsClass();
        String name = record.name();
        DnsRecordType type = record.type();

        short len = 0;
        byte[] data = null;

        if (record instanceof DnsRawRecord) {
            DnsRawRecord raw = (DnsRawRecord) record;
            ByteBuf buf = raw.content();
            len = (short) buf.readableBytes();
            if (len > 0) {
                data = new byte[buf.readableBytes()];
                buf.readBytes(data);
            }
        }

        return new DnsMessage.Record(name, (short) type.intValue(), (short) klass, ttl, len, data);
    }


    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        if (cause instanceof SerializationException) {
            log.warn("Serialize request occur a exception", cause);
        } else {
            log.error("An error occur in ProxyHandler", cause);
        }

        ctx.close();
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        super.channelInactive(ctx);
    }
}
