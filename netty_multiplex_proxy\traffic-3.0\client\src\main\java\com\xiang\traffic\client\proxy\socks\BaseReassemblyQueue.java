package com.xiang.traffic.client.proxy.socks;

import io.netty.buffer.CompositeByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.util.ReferenceCountUtil;

import java.util.Arrays;

/**
 * 用于实现UDP穿透中的分帧
 *
 * <AUTHOR>
 * @date 2024/6/6 15:12
 */
interface ReassemblyQueue {

    /**
     * @param frag    FRAG字段
     * @param message UDP报文
     * @return 整合后的UDP代理数据，如果返回null，则消息不完整或者超时
     */
    UdpProxyMessage tryAppendAndGet(byte frag, UdpProxyMessage message);

    /**
     * 重置ReassemblyQueue
     */
    void reset();
}


class BaseReassemblyQueue implements ReassemblyQueue {

    private UdpProxyMessage[] queue;

    private long lastAppendTime;

    private int nextFragId;

    public BaseReassemblyQueue() {
        this.lastAppendTime = -1;
        this.nextFragId = 1;
    }

    public UdpProxyMessage tryAppendAndGet(byte frag, UdpProxyMessage message) {
        if (lastAppendTime == -1) {
            lastAppendTime = System.currentTimeMillis();
        }

        if (queue == null) {
            queue = new UdpProxyMessage[4];
        }

        int index = frag & 0x7F;

        long now = System.currentTimeMillis();
        // frag不符合预期时抛弃
        if (index != nextFragId || now - lastAppendTime > 5000) {
            message.release();
            reset();
            return null;
        }

        if (queue[0] != null && !queue[0].isSameTarget(message)) {
            message.release();
            reset();
            return null;
        }

        // 检测Queue大小是否足够
        if (index - 1 >= queue.length) {
            if (queue.length * 2 > 128) {
                message.release();
                reset();
                return null;
            }

            UdpProxyMessage[] oldQueue = this.queue;
            this.queue = Arrays.copyOf(oldQueue, oldQueue.length * 2);
        }

        lastAppendTime = now;

        queue[index - 1] = message.retain();
        nextFragId++;

        // 最高位为1
        if (frag < 0) {
            UdpProxyMessage result = combine();
            reset();
            return result;
        }

        return null;
    }


    private UdpProxyMessage combine() {
        UdpProxyMessage first = queue[0];
        String host = first.getHost();
        int port = first.getPort();

        CompositeByteBuf buf = Unpooled.compositeBuffer(nextFragId - 1);
        for (UdpProxyMessage msg : queue) {
            buf.addComponent(true, msg.getData());
        }

        return new UdpProxyMessage(host, port, buf);
    }


    public void reset() {
        if (queue != null) {
            for (UdpProxyMessage msg : queue) {
                if (msg != null) {
                    ReferenceCountUtil.release(msg);
                }
            }
        }

        queue = null;
        lastAppendTime = -1;
        nextFragId = 1;
    }

    @Override
    protected void finalize() {
        reset();
    }
}
