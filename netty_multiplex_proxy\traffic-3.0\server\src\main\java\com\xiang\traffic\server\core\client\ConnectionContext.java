package com.xiang.traffic.server.core.client;

import com.xiang.traffic.protocol.AuthRequestMessage;
import com.xiang.traffic.server.core.ClientSession;
import com.xiang.traffic.server.core.ProxyProcessor;
import com.xiang.traffic.server.core.ProxyTaskManager;
import io.netty.channel.Channel;
import io.netty.resolver.dns.DnsNameResolver;
import io.netty.util.concurrent.FastThreadLocal;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.function.Predicate;

/**
 * 用于保存每个客户端连接所需要的参数
 *
 * <AUTHOR>
 * @date 2024/6/6 15:19
 */
final class ConnectionContext {
    private static final FastThreadLocal<Map<Channel, ConnectionContext>> CONTEXT = new FastThreadLocal<Map<Channel, ConnectionContext>>() {
        @Override
        protected Map<Channel, ConnectionContext> initialValue() {
            return new HashMap<>();
        }
    };

    /**
     * 客户端Session
     */
    private ClientSession clientSession;

    /**
     * 用于发布代理任务
     */
    private ProxyTaskManager proxyTaskManager;

    /**
     * 认证逻辑
     */
    private Predicate<AuthRequestMessage> authPredicate;

    /**
     * DNS客户端
     */
    private DnsNameResolver nameResolver;


    private ConnectionContext() {
    }

    /**
     * 初始化ConnectionContext
     *
     * @param channel          客户端与服务端连接的 {@link io.netty.channel.socket.SocketChannel}
     * @param proxyTaskManager 发布代理任务 {@link ProxyProcessor}
     * @param authPredicate    认证逻辑 {@link ClientProcessor#doAuth(AuthRequestMessage)}
     */
    static void initial(Channel channel, ProxyTaskManager proxyTaskManager, Predicate<AuthRequestMessage> authPredicate,
                        DnsNameResolver nameResolver) {
        accessCheckout(channel);
        ConnectionContext ctx = new ConnectionContext();
        ctx.proxyTaskManager = Objects.requireNonNull(proxyTaskManager);
        ctx.authPredicate = Objects.requireNonNull(authPredicate);
        ctx.nameResolver = Objects.requireNonNull(nameResolver);

        Map<Channel, ConnectionContext> map = CONTEXT.get();
        map.put(channel, ctx);

//        channel.closeFuture().addListener(future -> map.remove(channel));
    }

    static void destroy(Channel ch) {
        Map<Channel, ConnectionContext> map = CONTEXT.get();

        //清掉session
        ConnectionContext connectionContext = map.get(ch);

        //老用户clientSession中ClientSession.SQUEEZED_OFFLINE_ATTR属性为true时，新用户把老用户挤下线，该session是新用户session,不要回收session
        String username = connectionContext.clientSession.getAuthToken().getPrinciple().getUsername();
        ClientSession clientSession = ConnectionContext.clientSession(ch);
        boolean flag = clientSession.getAttributes().get(ClientSession.SQUEEZED_OFFLINE_ATTR) != null && (boolean) clientSession.getAttributes().get(ClientSession.SQUEEZED_OFFLINE_ATTR);
        if (!flag) {
            ClientSession.clientLogout(username);
        }

        map.remove(ch);
    }


    static ClientSession clientSession(Channel channel) {
        accessCheckout(channel);
        Map<Channel, ConnectionContext> map = CONTEXT.get();
        ConnectionContext ctx = map.get(channel);
        if (ctx == null) {
            ctx = new ConnectionContext();
            map.put(channel, ctx);
        }

        return ctx.clientSession;
    }

    static void putClientSession(Channel channel, ClientSession clientSession) {
        accessCheckout(channel);
        Objects.requireNonNull(clientSession);
        Map<Channel, ConnectionContext> map = CONTEXT.get();
        ConnectionContext ctx = map.get(channel);
        if (ctx == null) {
            throw new IllegalStateException("Not initialize");
        }

        ctx.clientSession = clientSession;
    }

    static ProxyTaskManager proxyTaskManager(Channel channel) {
        accessCheckout(channel);
        Map<Channel, ConnectionContext> map = CONTEXT.get();
        ConnectionContext ctx = map.get(channel);
        if (ctx == null) {
            ctx = new ConnectionContext();
            map.put(channel, ctx);
        }

        return ctx.proxyTaskManager;
    }

    static Predicate<AuthRequestMessage> authPredicate(Channel channel) {
        accessCheckout(channel);
        Map<Channel, ConnectionContext> map = CONTEXT.get();
        ConnectionContext ctx = map.get(channel);
        if (ctx == null) {
            ctx = new ConnectionContext();
            map.put(channel, ctx);
        }

        return ctx.authPredicate;
    }


    static DnsNameResolver nameResolver(Channel channel) {
        accessCheckout(channel);
        Map<Channel, ConnectionContext> map = CONTEXT.get();
        ConnectionContext ctx = map.get(channel);
        if (ctx == null) {
            ctx = new ConnectionContext();
            map.put(channel, ctx);
        }

        return ctx.nameResolver;
    }


    private static void accessCheckout(Channel channel) {
        if (!channel.eventLoop().inEventLoop(Thread.currentThread())) {
            throw new IllegalStateException();
        }
    }
}
