package com.xiang.traffic.server.core;

import com.xiang.traffic.AbstractComponent;
import com.xiang.traffic.AbstractSession;
import com.xiang.traffic.Session;
import com.xiang.traffic.protocol.AuthRequestMessage;
import com.xiang.traffic.protocol.DisconnectMessage;
import com.xiang.traffic.server.ServerConfig;
import com.xiang.traffic.server.core.client.ClientProcessor;
import com.xiang.traffic.server.core.token.AbstractAuthToken;
import com.xiang.traffic.server.core.token.SimpleToken;
import com.xiang.traffic.server.core.token.UserToken;
import com.xiang.traffic.server.db.user.UserDatabase;
import com.xiang.traffic.server.db.vo.DbUserVo;
import com.xiang.traffic.server.db.vo.SimpleUserVo;
import com.xiang.traffic.server.enumeration.ClientAuthType;
import com.xiang.traffic.server.utils.Constants;
import io.netty.channel.Channel;
import io.netty.channel.socket.SocketChannel;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 客户端会话对象
 */
public class ClientSession extends AbstractSession implements Session {
    public static final String SQUEEZED_OFFLINE_ATTR = "__SQUEEZED_OFFLINE__";
    //用户名:客户端Session
    private static final Map<String, ClientSession> SESSIONS = new ConcurrentHashMap<>();

    /**
     * 该客户端是否通过认证
     */
    private boolean auth = false;
    private AbstractAuthToken<?> authToken;

    public ClientSession(Channel channel) {
        super((SocketChannel) channel);
    }

    public boolean isWriteable() {
        return socketChannel.isWritable();
    }

    private final Map<String, Object> attributes = new HashMap<>();

    /**
     * 客户端登陆
     *
     * @param username
     * @param clientSession
     */
    public static void clientLogin(String username, ClientSession clientSession) {
        disconnectLoginedUser(username);

        //clientSession.socketChannel().close();
        //下面这句会先于clientSession.close()触发的事件channelInactive执行，channelInactive事件触发时SESSIONS中是新用户session
        SESSIONS.put(username, clientSession);
    }

    /**
     * 断开已登录用户连接
     */
    private static void disconnectLoginedUser(String username) {
        ClientSession clientSession = SESSIONS.get(username);
        if (clientSession != null) {
            DisconnectMessage disconnectMessage = new DisconnectMessage();
            clientSession.socketChannel().writeAndFlush(disconnectMessage);

            //用户被挤掉
            clientSession.getAttributes().put(ClientSession.SQUEEZED_OFFLINE_ATTR, true);

            clientSession.socketChannel().close();
        }
    }

    /**
     * 客户端登出
     *
     * @param username
     */
    public static void clientLogout(String username) {
        SESSIONS.remove(username);
    }

    public void writeMessage(Object msg) {
        checkChannelState();
        socketChannel.write(msg, socketChannel.voidPromise());
    }

    public void writeAndFlushMessage(Object msg) {
        checkChannelState();
        socketChannel.writeAndFlush(msg, socketChannel.voidPromise());
    }

    public void flushMessage() {
        checkChannelState();
        socketChannel.flush();
    }

    public boolean isAuth() {
        return auth;
    }

    public void passAuth(AbstractComponent<ClientProcessor> component, AuthRequestMessage authRequestMessage) {
        auth = true;

        ServerConfig.Node node = component.getParentComponent().getParentComponent().getServerConfig();
        UserDatabase db = component.getParentComponent().getParentComponent().getParentComponent().getUserDatabase();

        if (node.authType.equals(ClientAuthType.SIMPLE)) {
            SimpleUserVo userVo = new SimpleUserVo();
            userVo.setWriteLimit(node.writeLimit);
            userVo.setReadLimit(node.readLimit);

            authToken = new SimpleToken(userVo, remoteAddress().getAddress().getHostAddress());

            ClientSession.clientLogin(userVo.getUsername(), this);
        } else if (node.authType.equals(ClientAuthType.USER)) {
            String group = node.getArgument(Constants.GROUP_PARAM_NAME);
            UserDatabase.UserGroup userGroup = db.getUserGroup(group);

            String user = authRequestMessage.getParameter(Constants.USER_PARAM_NAME);
            DbUserVo dbUserVo = userGroup.getUserMap().get(user);

            authToken = new UserToken(dbUserVo, remoteAddress().getAddress().getHostAddress());

            ClientSession.clientLogin(dbUserVo.getUsername(), this);
        }
    }

    public void updateLastActiveTime() {
        lastActiveTime = System.currentTimeMillis();
    }

    private void checkChannelState() throws IllegalStateException {
        if (!isActive()) throw new IllegalStateException("Channel has been closed");
    }

    public AbstractAuthToken<?> getAuthToken() {
        return authToken;
    }

    @Override
    public String toString() {
        return "ClientSession [ address:" + remoteAddress() + " ,connection_time:" + connectionTime + " ,last_active_time:" + lastActiveTime + " ,auth:" + auth + " ]";
    }

    public Map<String, Object> getAttributes() {
        return attributes;
    }
}
